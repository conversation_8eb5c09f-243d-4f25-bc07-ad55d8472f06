# gtbcusage Module - English language file
Module=gtbcusage
gtbcusageDesc=Module for generating GTB Aging reports

# Setup page specific
gtbcusageSetupPageTitle=GTB Aging Module Setup
gtbcusageModule=GTB Aging
gtbcusageGeneralOptions=General Options

# Permissions
gtbcusage_CanUse=Can use GTB Aging Reports
gtbcusage_AddReport=Can create new reports
gtbcusage_EditReport=Can edit existing reports
gtbcusage_DeleteReport=Can delete reports
gtbcusage_ExportReport=Can export reports

# UI Elements
gtbcusageReport=GTB Aging Report
gtbcusageReportDetail=GTB Aging Report Detail
NotEnoughRights=You don't have sufficient permissions to perform this action
OverdueInterest=Overdue Interest

# Export options
PDF=PDF
CSV=CSV
gtbcusage_InterestPercentage=Interest Percentage
gtbcusage_InterestPercentageHelp=Enter the default annual interest rate (e.g., 2.5 for 2.5%).
gtbcusage_ProductAccount=Product Account to use for Interest
gtbcusage_ProductAccountHelp=Select a predefined service product used to represent accrued interest on invoices.
gtbcusage_DayOfMonth=Day in the month to calculate Interest
gtbcusage_Month=Month
ErrorProductNotFoundOrNotService=Error: Product %s not found or is not a service.

# Invoice Creation Process
InvoiceCreationResultTitle=Invoice Creation Result
InterestInvoiceLineDescription=Interest charge late payments (2 percent) for %s
InvoiceCreationErrorTitle=Invoice Creation Failed
InvoiceCreationSuccessTitle=Invoice Creation Successful
NoInvoicesCreatedTitle=No Invoices Created
ErrorNoDataForInvoiceCreation=Error: No data found in session for invoice creation. Please generate the data from the report page first.
InvalidCSRFToken=Error: Invalid security token. Please try again.
Home=Home

# Report specific translations
CustomerAgingReport=Customer Aging Report
CustomerAgingDetailReport=Customer Aging Detail Report
AllBranches=All Branches
AllCustomers=All Customers
Branch=Branch
Customer=Customer
Period=Period
CustomerName=Customer Name
TotalDue=Total Due
Totals=Totals
NoDataFound=No Data Found
InvoiceRef=Invoice Reference
ValidationDate=Validation Date
Filter=Filter
Excel_Total_Export=Export Total (Excel)
Excel_Detail_Export=Export Detail (Excel)
EnableShowDetailToExportDetail=Enable 'Show Detail' option to export detail data

# Supplier specific translations
SupplierAgingReport=Supplier Aging Report
SupplierAgingDetailReport=Supplier Aging Detail Report
AllSuppliers=All Suppliers
Supplier=Supplier
SupplierName=Supplier Name

# Common aging periods
Future=Future
Current=Current
30Days=30 Days
60Days=60 Days
90Days=90 Days
120DaysPlus=120 Days+

# Month names (for date formatting)
January=January
February=February
March=March
April=April
May=May
June=June
July=July
August=August
September=September
October=October
November=November
December=December
