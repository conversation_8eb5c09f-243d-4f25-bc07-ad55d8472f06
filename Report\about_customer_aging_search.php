<?php
// Load Dolibarr environment
$res = 0;
if (!$res && file_exists("../../main.inc.php")) $res = @include '../../main.inc.php'; // For custom modules
if (!$res && file_exists("../../../main.inc.php")) $res = @include '../../../main.inc.php'; // For deeper paths
if (!$res) die("Include of main fails");

// Check user rights
if (empty($user->rights->societe->lire)) accessforbidden(); // Ensure the user has the required permissions

// Load classes
require_once DOL_DOCUMENT_ROOT . '/core/lib/admin.lib.php';

// Output the page
llxHeader('', 'About Customer Aging Search Report');

print_fiche_titre('About Customer Aging Search Report', '', 'bill');

// Add close button under the heading
echo '<div class="close-button-container top-button">
    <button class="close-button" onclick="window.close();">Close</button>
</div>';

// Content from the markdown file
echo '<div class="markdown-content">
<h1>Customer Aging Search Report</h1>

<h2>Overview</h2>
<p>The Customer Aging Search Report provides a comprehensive view of outstanding customer invoices categorized by aging periods. This tool helps you track overdue payments and manage accounts receivable effectively.</p>

<h2>Key Features</h2>
<ul>
<li>Filter by branch, customer, month, and year</li>
<li>View aging categories: Current, 30 Days, 60 Days, 90 Days, and 120+ Days</li>
<li>Expandable invoice details for each customer</li>
<li>Search functionality for quick customer lookup</li>
<li>Payment term filtering and display options</li>
</ul>

<h2>How to Use</h2>
<ul>
<li>Select the desired branch from the dropdown menu</li>
<li>Choose a specific customer or view all customers</li>
<li>Set the month and year for the aging report</li>
<li>Use the checkboxes to show/hide additional information</li>
<li>Click on a customer row to expand and view individual invoice details</li>
<li>Use the search fields to filter customers by code or name</li>
<li>Click the Filter button to apply your selections, hide all expanded details, and reset the view</li>
</ul>

<h2>Search Functions</h2>
<ul>
<li><strong>Search Code</strong>: Type in this field to quickly filter customers by their customer code</li>
<li><strong>Search Customer Name</strong>: Type in this field to filter customers by their name</li>
</ul>
<p>Both search fields work in real-time as you type. You can use them together to narrow down results further.</p>

<h2>Filter Button</h2>
<p>The Filter button applies your selected criteria (branch, customer, month, year) and has these additional effects:</p>
<ul>
<li>Collapses all expanded customer details</li>
<li>Resets the view to show only the main customer rows</li>
<li>Clears any active search filters</li>
</ul>
<p>Use this button when you want to start fresh with new filter criteria.</p>

<h2>Aging Periods Explained</h2>
<ul>
<li><strong>Future</strong>: Invoices not yet due (negative days)</li>
<li><strong>Current</strong>: Invoices due within 0-30 days</li>
<li><strong>30 Days</strong>: Invoices overdue by 31-60 days</li>
<li><strong>60 Days</strong>: Invoices overdue by 61-90 days</li>
<li><strong>90 Days</strong>: Invoices overdue by 91-120 days</li>
<li><strong>120+ Days</strong>: Invoices overdue by more than 120 days</li>
</ul>

<h2>Tips for Effective Use</h2>
<ul>
<li>Regularly review the aging categories to identify overdue accounts</li>
<li>Use the search function to quickly locate specific customers</li>
<li>Export the report for sharing with your accounts team</li>
<li>Monitor trends in aging categories to identify payment pattern changes</li>
</ul>

<h2>Viewing Options</h2>
<p><strong>Full Screen Mode:</strong> Press the F11 key on your keyboard to toggle full screen mode. This removes browser toolbars and menus, providing maximum space for viewing your aging report data.</p>
<ul>
<li>Press F11 once to enter full screen mode</li>
<li>Press F11 again to exit and return to normal view</li>
</ul>
<p>Full screen mode is particularly useful when:</p>
<ul>
<li>Working with large datasets that require more vertical space</li>
<li>Presenting the report to others during meetings</li>
<li>Printing or taking screenshots of the complete report</li>
</ul>
<p>Note: The F11 function is a browser feature that works across most modern browsers (Chrome, Firefox, Edge, etc.) and is not specific to this report.</p>
</div>';

// Add close button at the bottom
echo '<div class="close-button-container bottom-button">
    <button class="close-button" onclick="window.close();">Close</button>
</div>';

// Add some basic styling
echo '<style>
    .markdown-content {
        max-width: 800px;
        margin: 20px auto;
        line-height: 1.6;
    }
    .markdown-content h1 {
        color: #2b4570;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
    }
    .markdown-content h2 {
        color: #2b4570;
        margin-top: 25px;
    }
    .markdown-content ul {
        margin-left: 20px;
    }
    .markdown-content p {
        margin: 15px 0;
    }
    .close-button-container {
        text-align: center;
        margin: 15px 0;
    }
    .top-button {
        margin-bottom: 20px;
    }
    .bottom-button {
        margin-top: 30px;
    }
    .close-button {
        padding: 8px 20px;
        background-color: #2b4570;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
    }
    .close-button:hover {
        background-color: #1a2a43;
    }
</style>';

llxFooter();
$db->close();
?>







