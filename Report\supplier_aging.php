<?php
// Load Dolibarr environment
$res = 0;
if (!$res && file_exists("../../main.inc.php")) $res = @include '../../main.inc.php'; // For custom modules
if (!$res && file_exists("../../../main.inc.php")) $res = @include '../../../main.inc.php'; // For deeper paths
if (!$res) die("Include of main fails");

// Check user rights
if (empty($user->rights->societe->lire)) accessforbidden(); // Ensure the user has the required permissions


// Get search parameters from POST or session
$search_code = GETPOST('search_code', 'alpha') ?: (isset($_SESSION['supplier_search_code']) ? $_SESSION['supplier_search_code'] : '');
$search_name = GETPOST('search_name', 'alpha') ?: (isset($_SESSION['supplier_search_name']) ? $_SESSION['supplier_search_name'] : '');
$selected_month = GETPOST('month', 'int') ?: (isset($_SESSION['supplier_selected_month']) ? $_SESSION['supplier_selected_month'] : date('n'));
$selected_year = GETPOST('year', 'int') ?: (isset($_SESSION['supplier_selected_year']) ? $_SESSION['supplier_selected_year'] : date('Y'));
$show_total_aging = GETPOST('show_total_aging', 'alpha') ? 'yes' : 'no'; // Added for Show Total Aging checkbox

// Get sort parameters
$sort_field = GETPOST('sort_field', 'alpha') ?: 'code'; // Default to code
$sort_order = GETPOST('sort_order', 'alpha') ?: 'asc'; // Default to ascending

// Make sure current_year is defined for the year dropdown
$current_year = date('Y');

// Save search parameters to session
$_SESSION['supplier_search_code'] = $search_code;
$_SESSION['supplier_search_name'] = $search_name;
$_SESSION['supplier_selected_month'] = $selected_month;
$_SESSION['supplier_selected_year'] = $selected_year;
$_SESSION['supplier_show_total_aging'] = $show_total_aging; // Save to session

// Clear search if reset button is clicked
if (GETPOST('button_removefilter_x', 'alpha') || GETPOST('button_removefilter.x', 'alpha') || GETPOST('button_removefilter', 'alpha')) {
    $search_code = '';
    $search_name = '';
    $_SESSION['supplier_search_code'] = '';
    $_SESSION['supplier_search_name'] = '';
    // Don't reset month/year as they're required fields
}

// Load license validation functions
require_once __DIR__ . '/../License/license_validation.php';

// Load classes
require_once DOL_DOCUMENT_ROOT . '/core/lib/admin.lib.php';
require_once DOL_DOCUMENT_ROOT . '/societe/class/societe.class.php';
require_once DOL_DOCUMENT_ROOT . '/fourn/class/fournisseur.facture.class.php';

// Define aging periods - remove Future
$aging_periods = [
    'Current' => [0, 30],
    '30 Days' => [31, 60],
    '60 Days' => [61, 90],
    '90 Days' => [91, 120],
    '120 Days+' => [121, 9999]
];

// Function to determine aging period based on validation date
function get_aging_period($validation_date, $filter_date) {
    global $aging_periods, $selected_month, $selected_year;

    // Ensure validation_date is numeric
    if (!is_numeric($validation_date)) {
        $validation_date = strtotime($validation_date);
    }

    // Ensure filter_date is numeric
    if (!is_numeric($filter_date)) {
        $filter_date = strtotime($filter_date);
    }

    // Get the month and year from validation date
    $validation_month = (int)date('n', $validation_date);
    $validation_year = (int)date('Y', $validation_date);

    // Convert selected month and year to integers for proper comparison
    $current_selected_month = (int)$selected_month; // Use a different variable name to avoid conflict
    $current_selected_year = (int)$selected_year;  // Use a different variable name to avoid conflict

    if ($validation_month == $current_selected_month && $validation_year == $current_selected_year) {
        return 'Current';
    }

    $days_from_validation = floor(($filter_date - $validation_date) / (60 * 60 * 24));

    foreach ($aging_periods as $label => $range) {
        if ($days_from_validation >= $range[0] && $days_from_validation <= $range[1]) {
            return $label;
        }
    }
    return 'Current';
}


// Add these lines near the top of the file after other variable initializations
// $selected_month and $selected_year are already defined above
$show_detail = GETPOST('show_detail', 'alpha') ?: 'yes'; // Though not used for a checkbox, kept for consistency if detail toggle is manual

// Add the calculate_due_date function (if needed for suppliers, though aging is usually by validation_date)
function calculate_due_date($invoice_date, $payment_term_type, $payment_term_days) {
    $invoice_timestamp = strtotime($invoice_date);

    if (empty($payment_term_type) && empty($payment_term_days)) {
        return $invoice_timestamp;
    }

    if ($payment_term_type == 1) { // End of month
        $last_day = date('t', $invoice_timestamp);
        $end_of_month = strtotime(date('Y-m-', $invoice_timestamp) . $last_day);
        return strtotime("+{$payment_term_days} days", $end_of_month);
    } else {
        return strtotime("+{$payment_term_days} days", $invoice_timestamp);
    }
}

// Create a timestamp for the first day of the selected month
$first_day_of_month = mktime(0, 0, 0, $selected_month, 1, $selected_year);

// Create a timestamp for the selected date (last day of the month)
$last_day = date('t', mktime(0, 0, 0, $selected_month, 1, $selected_year));
$filter_date = mktime(23, 59, 59, $selected_month, $last_day, $selected_year);

// Check if the selected month is licensed
$is_licensed = is_month_licensed($selected_month, $selected_year);
$license_indicator = $is_licensed ? '' : ' <span class="license-indicator unlicensed">(Unlicensed)</span>';

// Add debug output
// echo "<!-- License check: Month=" . $selected_month . ", Year=" . $selected_year . ", Licensed=" . ($is_licensed ? "Yes" : "No") . " -->";

// Initialize report data array - remove Future and Total Excl Future
$report_data = [];
$total_aging = [
    'Current' => 0, '30 Days' => 0, '60 Days' => 0, '90 Days' => 0, '120 Days+' => 0,
    'total' => 0
];

// Initialize filter variables before they're used in the SQL query
// These are not used in supplier aging search but kept for structural similarity if needed later
$selected_warehouse = GETPOST('filter_warehouse', 'alpha') ?: 'all';
$selected_supplier_filter = GETPOST('supplier_filter', 'int') ?: 'all'; // Renamed to avoid conflict
$selected_payment_term = GETPOST('payment_term', 'int') ?: 'all';


// SQL query for supplier invoices - only run if month is licensed
if ($is_licensed) {
    $sql = "
    SELECT 
        s.rowid AS supplier_id, 
        s.nom AS supplier_name, 
        s.code_fournisseur,
        f.rowid AS invoice_id,
        f.total_ttc - IFNULL((
            SELECT SUM(pf.amount) 
            FROM " . MAIN_DB_PREFIX . "paiementfourn_facturefourn AS pf 
            WHERE pf.fk_facturefourn = f.rowid
        ), 0) + COALESCE((
            SELECT SUM(cn.total_ttc) 
            FROM " . MAIN_DB_PREFIX . "facture_fourn AS cn
            WHERE cn.fk_soc = s.rowid AND cn.type = 2 AND cn.fk_facture_source = f.rowid
        ), 0) AS balance_due,
        f.paye, 
        f.ref AS invoice_ref, 
        f.datef AS invoice_date, 
        f.datef AS validation_date,
        COALESCE((
            SELECT SUM(cn.total_ttc) 
            FROM " . MAIN_DB_PREFIX . "facture_fourn AS cn
            WHERE cn.fk_soc = s.rowid AND cn.type = 2
        ), 0) AS credit_notes,
        COALESCE((
            SELECT SUM(pf.amount) 
            FROM " . MAIN_DB_PREFIX . "paiementfourn_facturefourn AS pf 
            WHERE pf.fk_facturefourn = f.rowid
        ), 0) AS payment_amount
    FROM 
        " . MAIN_DB_PREFIX . "societe AS s
    JOIN 
        " . MAIN_DB_PREFIX . "facture_fourn AS f ON s.rowid = f.fk_soc
    WHERE 
        f.fk_statut = 1 
        AND f.paye >= 0 
        AND f.total_ttc != 0";  

if (empty($search_code) && empty($search_name)) {
    $sql .= " AND f.datef <= '" . date('Y-m-d', $filter_date) . "'";
}

if (!empty($search_code)) {
    $sql .= " AND s.code_fournisseur LIKE '%" . $db->escape($search_code) . "%'";
}
if (!empty($search_name)) {
    $sql .= " AND s.nom LIKE '%" . $db->escape($search_name) . "%'";
}

$sql .= " HAVING balance_due != 0"; 

$resql = $db->query($sql);

}

if ($is_licensed && isset($resql) && $resql) {
    // $num_rows = $db->num_rows($resql);
    // echo "<!-- Number of rows returned: " . $num_rows . " -->";

    while ($obj = $db->fetch_object($resql)) {
        $supplier_id = $obj->supplier_id;
        $supplier_name = $obj->supplier_name;
        $supplier_code = $obj->code_fournisseur;
        $balance_due = (float)$obj->balance_due; // Ensure float
        $invoice_id = $obj->invoice_id;
        $invoice_ref = $obj->invoice_ref;
        $validation_date_str = $obj->validation_date;

        $current_filter_date = mktime(23, 59, 59, $selected_month, $last_day, $selected_year);
        $aging_period = get_aging_period($validation_date_str, $current_filter_date);

        if (abs($balance_due) < 0.01) continue; // Skip if effectively zero

        if (!isset($report_data[$supplier_id])) {
            $report_data[$supplier_id] = [
                'supplier_id' => $supplier_id,
                'supplier_name' => dol_escape_htmltag($supplier_name),
                'supplier_code' => dol_escape_htmltag($supplier_code),
                'invoices' => [],
                'Current' => 0, '30 Days' => 0, '60 Days' => 0, '90 Days' => 0, '120 Days+' => 0,
                'total' => 0
            ];
        }

        $report_data[$supplier_id][$aging_period] += $balance_due;
        $report_data[$supplier_id]['total'] += $balance_due;
        $total_aging[$aging_period] += $balance_due;
        $total_aging['total'] += $balance_due;

        $report_data[$supplier_id]['invoices'][] = [
            'invoice_id' => $invoice_id,
            'invoice_ref' => dol_escape_htmltag($invoice_ref),
            'validation_date' => $validation_date_str, // Store original string for display
            'aging_period' => $aging_period,
            'balance_due' => $balance_due,
            // Pre-calculate amounts for detail table display
            '120 Days+' => ($aging_period == '120 Days+') ? $balance_due : 0,
            '90 Days' => ($aging_period == '90 Days') ? $balance_due : 0,
            '60 Days' => ($aging_period == '60 Days') ? $balance_due : 0,
            '30 Days' => ($aging_period == '30 Days') ? $balance_due : 0,
            'Current' => ($aging_period == 'Current') ? $balance_due : 0,
        ];
    }

    $sorted_suppliers = $report_data;

    // Sort data based on $sort_field and $sort_order
    if (!empty($sorted_suppliers)) {
        uasort($sorted_suppliers, function ($a, $b) use ($sort_field, $sort_order) {
            if ($sort_field == 'code') {
                $val_a = $a['supplier_code'];
                $val_b = $b['supplier_code'];
                return ($sort_order == 'asc') ? strcmp($val_a, $val_b) : strcmp($val_b, $val_a);
            } elseif ($sort_field == 'supplier_name') {
                $val_a = $a['supplier_name'];
                $val_b = $b['supplier_name'];
                return ($sort_order == 'asc') ? strcmp($val_a, $val_b) : strcmp($val_b, $val_a);
            } elseif ($sort_field == 'total') {
                $val_a = (float)$a['total'];
                $val_b = (float)$b['total'];
                if ($val_a == $val_b) return 0;
                return ($sort_order == 'asc') ? ($val_a < $val_b ? -1 : 1) : ($val_b < $val_a ? -1 : 1);
            }
            return 0; // Default: no change in order
        });
    }
} else {
    $sorted_suppliers = [];
    // if (!$is_licensed) { echo "<!-- No data found: Month is not licensed -->"; }
    // else if (!isset($resql)) { echo "<!-- No data found: Query was not executed -->"; }
    // else { echo "<!-- No data found: Query failed - " . $db->lasterror() . " -->"; }
}

// Page title
$title = 'Supplier Aging Search Report ' . $license_indicator;
llxHeader('', $title);

print_fiche_titre('Supplier Aging Search Report ' . $license_indicator, '', 'bill');

// Add custom CSS for license status
echo '<style>' . file_get_contents(__DIR__ . '/../License/license_styles.css') . '</style>';
?>

<style>
/* General Styles */
.about-button-container { text-align: right; margin: 10px 0 15px 0; }
.about-button { padding: 6px 15px; background-color: #2b4570; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px; font-weight: bold; }
.about-button:hover { background-color: #1a2a43; }

/* Filter Styles */
.filter-container { display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 20px; align-items: flex-end; }
.filter-group { display: flex; flex-direction: column; min-width: 150px; }
.filter-group label { margin-bottom: 5px; font-weight: bold; }
.filter-group select, .filter-group input[type="text"], .filter-group input[type="submit"] { padding: 6px; border: 1px solid #ddd; border-radius: 4px; }
.filter-group input[type="checkbox"] { margin-right: 5px; }
.filter-info { margin-left: 10px; color: #555; font-style: italic; font-size: 0.9em; }

/* Table Styles */
.table-container { position: relative; max-height: 80vh; overflow-y: auto; margin-top:10px;}
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; background-color: #fff; /* Ensure background for sticky header */ }
thead { position: sticky; top: 0; z-index: 10; }
thead tr:first-child th { /* Search row */ background-color: #f8f8f8; top: 0; }
thead tr:nth-child(2) th { /* Header row */ background-color: #f2f2f2; top: 40px; /* Adjust based on search row height */ box-shadow: 0 2px 2px -1px rgba(0,0,0,0.1); }
th { font-weight: bold; text-align: right; }
th.text-left { text-align: left; }
td { text-align: right; }
td.text-left { text-align: left; }
.group-header { background-color: #ddd !important; font-weight: bold; } /* Ensure group header background overrides alternating */
tbody tr:nth-child(even):not(.group-header):not(.detail-row):not(.spacing-row) { background-color: #f9f9f9; }
tbody tr:not(.group-header):not(.detail-row):not(.spacing-row):hover { background-color: #f0f0f0 !important; } /* Hover for data rows */

/* Main Table Column Widths */
th:nt1h-child(1), td:nth-child(1) { width: 0.5%; text-align: left; } /* Code */
th:nth-child(2), td:nth-child(2) { width: 25%; text-align: left; } /* Supplier Name */
/* Amount Columns (3rd to 8th) */
th:nth-child(n+3), td:nth-child(n+3):not([colspan]) { width: 11.16%; text-align: right; }


/* Clickable Row & Detail Row Styles */
.clickable { cursor: pointer; }
.clickable td:first-child { position: relative; padding-left: 25px; } /* Space for icon */
.clickable td:first-child::before { content: "▶"; position: absolute; left: 8px; top: 50%; transform: translateY(-50%); font-size: 10px; color: #555; transition: transform 0.2s; }
.clickable.expanded td:first-child::before { transform: translateY(-50%) rotate(90deg); }
.expanded { background-color: #e0e5f0 !important; }
.expanded td { border-bottom: 2px solid #2b4570 !important; }
.detail-row > td { padding: 10px 15px; background-color: #f9f9f9 !important; } /* Ensure detail row background */

/* Nested Detail Table Styles */
.nested-table { width: 100%; margin: 5px 0; border: 1px solid #e0e0e0; border-collapse: collapse; }
.nested-table th, .nested-table td { padding: 5px; border: 1px solid #e7e7e7; }
.nested-table th { background-color: #f0f0f0; text-align: right; font-weight: bold; }
.nested-table td { text-align: right; }
/* Invoice Ref in Nested Table */
.nested-table th:nth-child(1), .nested-table td:nth-child(1) { width: 12%; text-align: left; }
/* Validation Date in Nested Table */
.nested-table th:nth-child(2), .nested-table td:nth-child(2) { width: 21%; text-align: left; }
/* Amount Columns in Nested Table */
.nested-table th:nth-child(n+3), .nested-table td:nth-child(n+3) { width: 11.16%; text-align: right; }

.spacing-row td { height: 10px; border: none; padding: 0; background-color: transparent !important; }
.spacing-row { height: 10px; }

/* Hide the CLEAR button */
input[name="button_removefilter"], input[name="button_removefilter_x"], input[name="button_removefilter.x"] { display: none !important; }
</style>
<style>
/* Make AMOUNTS in "Total for selected Suppliers" row red */
tr.group-header td:not(.text-left) { /* Targets td elements in group-header that are NOT the label */
    color: red;
}
</style>

<!-- Filter form -->
<form method="get" action="<?php echo $_SERVER['PHP_SELF']; ?>">
    <div class="filter-container">
        <div class="filter-group">
            <label for="month">Month:</label>
            <select name="month" id="month">
                <?php
                for ($i = 1; $i <= 12; $i++) {
                    $selected = ($i == $selected_month) ? 'selected="selected"' : '';
                    echo '<option value="' . $i . '" ' . $selected . '>' . date('F', mktime(0, 0, 0, $i, 1, 2000)) . '</option>';
                }
                ?>
            </select>
        </div>

        <div class="filter-group">
            <label for="year">Year:</label>
            <select name="year" id="year">
                <?php
                for ($i = $current_year - 5; $i <= $current_year + 1; $i++) {
                    $selected = ($i == $selected_year) ? 'selected="selected"' : '';
                    echo '<option value="' . $i . '" ' . $selected . '>' . $i . '</option>';
                }
                ?>
            </select>
        </div>
        <div class="filter-group">
            <label for="show_total_aging">Show Total Aging</label>
            <input type="checkbox" name="show_total_aging" id="show_total_aging" value="yes" <?php echo ($show_total_aging == 'yes') ? 'checked' : ''; ?> onchange="this.form.submit()">
        </div>

        <div class="filter-group">
            <label>&nbsp;</label> <!-- Spacer for alignment -->
            <input type="submit" name="filter" value="FILTER" class="button">
        </div>
        <div class="filter-group">
             <label>&nbsp;</label> <!-- Spacer for alignment -->
            <span class="filter-info">First Filter Month & Year before Searching Code or Name</span>
        </div>
    </div>
</form>

<?php if (!$is_licensed): ?>
<div class="info">Report data is not available for unlicensed months.</div>
<?php elseif (empty($sorted_suppliers) && (empty($search_code) && empty($search_name))): // Show "No data" only if no search and no results ?>
<div class="info">No data available for the selected period. Please ensure filters are correctly set or try searching.</div>
<?php elseif (empty($sorted_suppliers) && (!empty($search_code) || !empty($search_name))): // Show "No results" if search was performed ?>
<div class="info">No suppliers found matching your search criteria.</div>
<?php else: ?>
<div class="table-container">
<table>
<thead>
<tr class="search-row">
    <th><input type="text" id="codeSearch" class="search-input" placeholder="Search Code..." value="<?php echo dol_escape_htmltag($search_code); ?>" onkeyup="filterTable()"></th>
    <th><input type="text" id="nameSearch" class="search-input" placeholder="Search Supplier..." value="<?php echo dol_escape_htmltag($search_name); ?>" onkeyup="filterTable()"></th>
    <th></th><th></th><th></th><th></th><th></th><th></th> <!-- Placeholders for other 6 columns -->
</tr>
<tr>
    <?php
    // Construct URL parameters for sort links, preserving current filters
    $sort_url_params = '&month='.$selected_month
                     . '&year='.$selected_year
                     . '&show_total_aging='.$show_total_aging
                     . '&filter=FILTER'; // Crucial to trigger data display logic on GET
    ?>
    <th class="text-left sortable" onclick="window.location='<?php echo $_SERVER['PHP_SELF']
         .'?sort_field=code'
         .'&sort_order='.($sort_field == 'code' && $sort_order == 'asc' ? 'desc' : 'asc')
         .$sort_url_params ?>'">
        Code (sort)
        <?php if ($sort_field == 'code') { ?>
            <span class="sort-icon sort-<?php echo $sort_order ?>"></span>
        <?php } ?>
    </th>
    <th class="text-left sortable" onclick="window.location='<?php echo $_SERVER['PHP_SELF']
         .'?sort_field=supplier_name'
         .'&sort_order='.($sort_field == 'supplier_name' && $sort_order == 'asc' ? 'desc' : 'asc')
         .$sort_url_params ?>'">
        Supplier (sort)
    <?php if ($sort_field == 'supplier_name') { ?>
            <span class="sort-icon sort-<?php echo $sort_order ?>"></span>
        <?php } ?>
    </th>
    <th class="text-right">120 Days+</th>
    <th class="text-right">90 Days (<?php echo date('M', mktime(0, 0, 0, $selected_month - 3, 1, $selected_year)); ?>)</th>
    <th class="text-right">60 Days (<?php echo date('M', mktime(0, 0, 0, $selected_month - 2, 1, $selected_year)); ?>)</th>
    <th class="text-right">30 Days (<?php echo date('M', mktime(0, 0, 0, $selected_month - 1, 1, $selected_year)); ?>)</th>
    <th class="text-right">Current (<?php echo date('M', mktime(0, 0, 0, $selected_month, 1, $selected_year)); ?>)</th>
    <th class="text-right sortable" onclick="window.location='<?php echo $_SERVER['PHP_SELF']
         .'?sort_field=total'
         .'&sort_order='.($sort_field == 'total' && $sort_order == 'asc' ? 'desc' : 'asc')
         .$sort_url_params ?>'">
        Total (sort)
        <?php if ($sort_field == 'total') { ?>
            <span class="sort-icon sort-<?php echo $sort_order ?>"></span>
        <?php } ?>
    </th>
</tr>
</thead>
<tbody>
<?php if ($show_total_aging == 'yes'): ?>
    <tr class="group-header">
        <td class="text-left"></td> <!-- Empty for Code column -->
        <td class="text-left">Total for selected Suppliers</td> <!-- Label for Supplier column -->
        <td><?php echo price($total_aging['120 Days+']); ?></td>
        <td><?php echo price($total_aging['90 Days']); ?></td>
        <td><?php echo price($total_aging['60 Days']); ?></td>
        <td><?php echo price($total_aging['30 Days']); ?></td>
        <td><?php echo price($total_aging['Current']); ?></td>
        <td><strong><?php echo price($total_aging['total']); ?></strong></td>
    </tr>
    <tr class="spacing-row"><td colspan="8">&nbsp;</td></tr>
<?php endif; ?>

<?php foreach ($sorted_suppliers as $supplier_id => $supplier_data): ?>
    <tr id="supplier-row-<?php echo $supplier_id; ?>" class="clickable">
        <td class="text-left"><?php echo $supplier_data['supplier_code']; ?></td>
        <td class="text-left"><?php echo $supplier_data['supplier_name']; ?></td>
        <td><?php echo price($supplier_data['120 Days+']); ?></td>
        <td><?php echo price($supplier_data['90 Days']); ?></td>
        <td><?php echo price($supplier_data['60 Days']); ?></td>
        <td><?php echo price($supplier_data['30 Days']); ?></td>
        <td><?php echo price($supplier_data['Current']); ?></td>
        <td><strong><?php echo price($supplier_data['total']); ?></strong></td>
    </tr>
    <tr id="detail-row-<?php echo $supplier_id; ?>" class="detail-row" style="display: none;">
        <td colspan="8">
            <table class="nested-table">
                <thead>
                    <tr>
                        <th class="text-left">Invoice Ref</th>
                        <th class="text-left">Validation Date</th>
                        <th>120 Days+</th>
                        <th>90 Days</th>
                        <th>60 Days</th>
                        <th>30 Days</th>
                        <th>Current</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($supplier_data['invoices'])): ?>
                        <?php foreach ($supplier_data['invoices'] as $invoice): ?>
                            <tr>
                                <td class="text-left">
                                    <a href="<?php echo DOL_URL_ROOT; ?>/fourn/facture/card.php?facid=<?php echo $invoice['invoice_id']; ?>" target="_blank">
                                        <?php echo $invoice['invoice_ref']; ?>
                                    </a>
                                </td>
                                <td class="text-left"><?php echo dol_print_date($invoice['validation_date'], 'day'); ?></td>
                                <td><?php echo price($invoice['120 Days+']); ?></td>
                                <td><?php echo price($invoice['90 Days']); ?></td>
                                <td><?php echo price($invoice['60 Days']); ?></td>
                                <td><?php echo price($invoice['30 Days']); ?></td>
                                <td><?php echo price($invoice['Current']); ?></td>
                                <td><strong><?php echo price($invoice['balance_due']); ?></strong></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr><td colspan="8" style="text-align: center;">No details available.</td></tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </td>
    </tr>
    <tr class="spacing-row" style="display: none;"><td colspan="8">&nbsp;</td></tr>
<?php endforeach; ?>
</tbody>
</table>
</div><!-- Close table-container -->
<?php endif; ?>

<script type="text/javascript">
function filterTable() {
    var codeSearch = document.getElementById('codeSearch').value.toLowerCase();
    var nameSearch = document.getElementById('nameSearch').value.toLowerCase();
    var supplierRows = document.querySelectorAll('tbody tr.clickable'); // Target only clickable supplier rows in tbody
    var matchedCount = 0;

    for (var i = 0; i < supplierRows.length; i++) {
        var row = supplierRows[i];
        var codeCell = row.cells[0];
        var nameCell = row.cells[1];
        var codeText = codeCell ? codeCell.textContent.toLowerCase() : '';
        var nameText = nameCell ? nameCell.textContent.toLowerCase() : '';
        var detailRow = document.getElementById('detail-row-' + row.id.replace('supplier-row-', ''));
        var spacingRowAfterDetail = detailRow ? detailRow.nextElementSibling : null;
        if (spacingRowAfterDetail && !spacingRowAfterDetail.classList.contains('spacing-row')) {
            spacingRowAfterDetail = null; // Ensure it's actually a spacing row
        }


        var matchesCode = codeSearch === '' || codeText.includes(codeSearch);
        var matchesName = nameSearch === '' || nameText.includes(nameSearch);
        var shouldShow = false;

        if (codeSearch !== '') {
            shouldShow = matchesCode;
        } else if (nameSearch !== '') {
            shouldShow = matchesName;
        } else {
            shouldShow = true; // Show all if no search
        }

        if (shouldShow) {
            row.style.display = 'table-row';
            matchedCount++;
            // If row is shown and was expanded, keep detail row visible
            if (detailRow && row.classList.contains('expanded')) {
                detailRow.style.display = 'table-row';
                if (spacingRowAfterDetail) spacingRowAfterDetail.style.display = 'table-row';
            } else if (detailRow) { // If row is shown but wasn't expanded, ensure detail is hidden
                detailRow.style.display = 'none';
                 if (spacingRowAfterDetail) spacingRowAfterDetail.style.display = 'none';
            }
        } else {
            row.style.display = 'none';
            if (detailRow) detailRow.style.display = 'none'; // Hide detail if parent is hidden
            if (spacingRowAfterDetail) spacingRowAfterDetail.style.display = 'none';
            row.classList.remove('expanded'); // Collapse if hidden
        }
    }
     // Auto-expand if only one result from search
    if (matchedCount === 1 && (codeSearch !== '' || nameSearch !== '')) {
        for (var i = 0; i < supplierRows.length; i++) {
            if (supplierRows[i].style.display === 'table-row') {
                var detailRow = document.getElementById('detail-row-' + supplierRows[i].id.replace('supplier-row-', ''));
                var spacingRow = detailRow ? detailRow.nextElementSibling : null;
                if (detailRow) {
                    supplierRows[i].classList.add('expanded');
                    detailRow.style.display = 'table-row';
                    if (spacingRow && spacingRow.classList.contains('spacing-row')) spacingRow.style.display = 'table-row';
                }
                break; 
            }
        }
    }
}


function toggleDetail(row) {
    var supplierId = row.id.replace('supplier-row-', '');
    var detailRow = document.getElementById('detail-row-' + supplierId);
    var spacingRow = detailRow ? detailRow.nextElementSibling : null;
    if (spacingRow && !spacingRow.classList.contains('spacing-row')) {
        spacingRow = null; // Not a valid spacing row
    }

    if (detailRow) {
        row.classList.toggle('expanded');
        if (row.classList.contains('expanded')) {
            detailRow.style.display = 'table-row';
            if (spacingRow) spacingRow.style.display = 'table-row';
        } else {
            detailRow.style.display = 'none';
            if (spacingRow) spacingRow.style.display = 'none';
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    var supplierRows = document.querySelectorAll('tr.clickable');
    for (var i = 0; i < supplierRows.length; i++) {
        supplierRows[i].addEventListener('click', function() {
            toggleDetail(this);
        });
        // Initially hide all detail rows and their spacing rows
        var detailRow = document.getElementById('detail-row-' + supplierRows[i].id.replace('supplier-row-', ''));
        if (detailRow) {
            detailRow.style.display = 'none';
            var spacingRow = detailRow.nextElementSibling;
            if (spacingRow && spacingRow.classList.contains('spacing-row')) {
                spacingRow.style.display = 'none';
            }
        }
    }
    // Initial filter call if search boxes have values (e.g., from page reload with session values)
    if (document.getElementById('codeSearch').value !== '' || document.getElementById('nameSearch').value !== '') {
        filterTable();
    }
});
</script>

<?php
llxFooter();
$db->close();
?>
