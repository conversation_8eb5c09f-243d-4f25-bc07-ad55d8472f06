

<?php
// Load Dolibarr environment
$res = 0;
if (!$res && file_exists("../../main.inc.php")) $res = @include '../../main.inc.php'; // For custom modules
if (!$res && file_exists("../../../main.inc.php")) $res = @include '../../../main.inc.php'; // For deeper paths
if (!$res) die("Include of main fails");

// AJAX Handler for fetching customers based on warehouse
if (isset($_GET['action']) && $_GET['action'] == 'fetch_customers') {
    global $db, $conf; // Ensure $db and $conf are available
    header('Content-Type: application/json');
    $warehouse_id = GETPOST('warehouse_id', 'alpha'); // 'alpha' for 'all' or int
    $customers = array();

    $sql_ajax_customers = "SELECT s.rowid, s.nom FROM " . MAIN_DB_PREFIX . "societe as s WHERE s.client >= 1";
    // Add entity check if multicompany is enabled and relevant for your setup
    // $sql_ajax_customers .= " AND s.entity IN (" . getEntity('societe') . ")";

    $sql_ajax_customers .= " ORDER BY s.nom ASC";

    $resql_ajax_customers = $db->query($sql_ajax_customers);
    if ($resql_ajax_customers) {
        while ($obj = $db->fetch_object($resql_ajax_customers)) {
            $customers[] = array('id' => $obj->rowid, 'name' => dol_escape_htmltag($obj->nom));
        }
    }
    echo json_encode($customers);
    exit;
}

// Check user rights
if (empty($user->rights->societe->lire)) accessforbidden(); // Ensure the user has the required permissions

// Load license validation functions
require_once __DIR__ . '/../License/license_validation.php';

// Load classes
require_once DOL_DOCUMENT_ROOT . '/core/lib/admin.lib.php';
require_once DOL_DOCUMENT_ROOT . '/societe/class/societe.class.php';
require_once DOL_DOCUMENT_ROOT . '/compta/facture/class/facture.class.php';

// Handle filter values
$selected_customer = isset($_POST['customer']) ? $_POST['customer'] : 'all';
$selected_month = isset($_POST['filter_month']) ? intval($_POST['filter_month']) : date('n');
$selected_year = isset($_POST['filter_year']) ? intval($_POST['filter_year']) : date('Y');
$show_detail = 'yes'; // Always set to 'yes' regardless of POST value
$show_total_aging = isset($_POST['show_total_aging']) ? 'yes' : 'no';
$selected_currency_usd = isset($_POST['filter_currency_usd']) ? 'yes' : 'no'; // 'yes' for USD, 'no' for CAD
$show_payment_term = 'no';


// Set filter date to last day of selected month
$filter_date = strtotime($selected_year . '-' . $selected_month . '-' . date('t', strtotime($selected_year . '-' . $selected_month . '-01')));

// Sort settings
$sort_field = isset($_POST['sort_field']) ? $_POST['sort_field'] : 'customer_name';
$sort_order = isset($_POST['sort_order']) ? $_POST['sort_order'] : 'asc';

// Check if the selected month is licensed
$is_licensed = is_month_licensed($selected_month, $selected_year);
$license_status_message = get_license_status_message($selected_month, $selected_year);
$license_indicator = $is_licensed ? '' : ' <span class="license-indicator unlicensed">(Unlicensed)</span>';

// Initialize arrays for storing data
$report_data = [];
$total_aging = [
    'Future' => 0,
    'Current' => 0,
    '30 Days' => 0,
    '60 Days' => 0,
    '90 Days' => 0,
    '120 Days+' => 0,
    'total' => 0,
    'total_excl_future' => 0
];
$sorted_customers = [];

// Define aging periods
$aging_periods = [
    'Future' => [-9999, -1],  // For invoices due in the future
    'Current' => [0, 29],     // 0-29 days overdue
    '30 Days' => [30, 59],    // 30-59 days overdue
    '60 Days' => [60, 89],    // 60-89 days overdue
    '90 Days' => [90, 119],   // 90-119 days overdue
    '120 Days+' => [120, 9999] // 120+ days overdue
];

// Function to determine aging period
function get_aging_period($days) {
    global $aging_periods;
    foreach ($aging_periods as $label => $range) {
        if ($days >= $range[0] && $days <= $range[1]) {
            return $label;
        }
    }
    return 'Unknown';
}

// Function to calculate due date based on payment terms
function calculate_due_date($invoice_date, $payment_term_id) {
    global $paymentTermsMapping, $db;

    // If payment term not found in our mapping, try to fetch it directly
    if (!isset($paymentTermsMapping[$payment_term_id]) || empty($payment_term_id)) {
        // Try to get payment term details from database
        $sql = "SELECT rowid, code, libelle, nbjour, type_cdr, decalage FROM " . MAIN_DB_PREFIX . "c_payment_term WHERE rowid = " . intval($payment_term_id);
        $resql = $db->query($sql);
        if ($resql && $obj = $db->fetch_object($resql)) {
            $paymentTermsMapping[$payment_term_id] = [
                'label' => $obj->libelle,
                'code' => $obj->code,
                'nbjour' => intval($obj->nbjour),
                'type_cdr' => intval($obj->type_cdr),
                'decalage' => intval($obj->decalage)
            ];
        } else {
            // Default to 60 days end of month if payment term is unknown
            $invoice_timestamp = is_numeric($invoice_date) ? $invoice_date : strtotime($invoice_date);
            if ($invoice_timestamp === false) {
                $invoice_timestamp = time();
            }

            // Add 60 days
            $due_date = strtotime('+60 days', $invoice_timestamp);

            // Move to end of month
            $due_date = strtotime('last day of this month', $due_date);

            return $due_date;
        }
    }

    $term = $paymentTermsMapping[$payment_term_id];

    // Convert invoice date to timestamp if it's not already
    $invoice_timestamp = is_numeric($invoice_date) ? $invoice_date : strtotime($invoice_date);

    if ($invoice_timestamp === false) {
        // If conversion fails, return current timestamp
        return time();
    }

    // Start with invoice date
    $due_date = $invoice_timestamp;

    // Add the number of days specified in the payment term
    if (isset($term['nbjour']) && $term['nbjour'] > 0) {
        $due_date = strtotime('+' . intval($term['nbjour']) . ' days', $due_date);
    }

    // If type_cdr is 1, it means "end of month"
    if (isset($term['type_cdr']) && $term['type_cdr'] == 1) {
        // Move to the end of the month
        $due_date = strtotime('last day of this month', $due_date);

        // Apply decalage (additional days) if specified
        if (isset($term['decalage']) && $term['decalage'] > 0) {
            $due_date = strtotime('+' . intval($term['decalage']) . ' days', $due_date);
        }
    }

    return $due_date;
}

// Fetch warehouse options from societe table (using the fk_warehouse column)
$warehouse_options = ['all' => 'All']; // Changed from 'All Branches' to just 'All'

// Handle filter values
$selected_customer = isset($_POST['customer']) ? $_POST['customer'] : 'all';
$selected_month = isset($_POST['filter_month']) ? intval($_POST['filter_month']) : date('n');
$selected_year = isset($_POST['filter_year']) ? intval($_POST['filter_year']) : date('Y');
$show_detail = 'yes'; // Always set to 'yes' regardless of POST value
$show_total_aging = isset($_POST['show_total_aging']) ? 'yes' : 'no';
// Create a timestamp for the selected date (last day of the month)
$last_day = date('t', mktime(0, 0, 0, $selected_month, 1, $selected_year));
$filter_date = mktime(23, 59, 59, $selected_month, $last_day, $selected_year);

// Fetch customer list
$sql_customers = "SELECT rowid, nom, code_client FROM " . MAIN_DB_PREFIX . "societe WHERE client >= 1 ORDER BY nom ASC";
$resql_customers = $db->query($sql_customers);
$customer_options = ['all' => 'All Customers'];
if ($resql_customers) {
    while ($customer = $db->fetch_object($resql_customers)) {
        $customer_options[$customer->rowid] = dol_escape_htmltag($customer->nom);
    }
}

// Build mapping from payment term id to label and details
$sql_payment_terms = "SELECT rowid, code, libelle, nbjour, type_cdr, decalage FROM " . MAIN_DB_PREFIX . "c_payment_term WHERE active = 1";
$resql_payment_terms = $db->query($sql_payment_terms);
$paymentTermsMapping = [];
$paymentTermOptions = ['all' => 'All Payment Terms'];
if ($resql_payment_terms) {
    while ($row = $db->fetch_object($resql_payment_terms)) {
        $paymentTermsMapping[$row->rowid] = [
            'label' => $row->libelle,
            'code' => $row->code,
            'nbjour' => intval($row->nbjour),
            'type_cdr' => intval($row->type_cdr),
            'decalage' => intval($row->decalage)
        ];
        $paymentTermOptions[$row->rowid] = $row->libelle;
    }
}

if ($is_licensed) {
    $sql = "SELECT s.rowid AS customer_id, s.nom AS customer_name, s.code_client, s.cond_reglement AS payment_term, f.rowid AS invoice_id,
    f.total_ttc - IFNULL(( SELECT SUM(pf.amount) FROM " . MAIN_DB_PREFIX . "paiement_facture AS pf WHERE pf.fk_facture = f.rowid ), 0) AS total_after_payments,
    IFNULL(( SELECT SUM(cn.total_ttc) FROM " . MAIN_DB_PREFIX . "facture AS cn WHERE cn.fk_facture_source = f.rowid AND cn.type = 2 ), 0) AS total_credit_notes,
    (f.total_ttc - IFNULL(( SELECT SUM(pf.amount) FROM " . MAIN_DB_PREFIX . "paiement_facture AS pf WHERE pf.fk_facture = f.rowid ), 0) + IFNULL(( SELECT SUM(cn.total_ttc) FROM " . MAIN_DB_PREFIX . "facture AS cn WHERE cn.fk_facture_source = f.rowid AND cn.type = 2 ), 0)) AS balance_due,
    f.date_lim_reglement, f.paye, s.fk_warehouse, f.ref AS invoice_ref, f.datef AS invoice_date,
    f.date_valid, f.fk_statut AS invoice_status,
    s.cond_reglement AS customer_payment_term
    FROM " . MAIN_DB_PREFIX . "societe AS s
    JOIN " . MAIN_DB_PREFIX . "facture AS f ON s.rowid = f.fk_soc
    WHERE f.type != 2 AND f.paye = 0 AND f.fk_statut > 0";

    if ($selected_customer != 'all') {
        $sql .= " AND s.rowid = " . intval($selected_customer);
    }

    if ($selected_currency_usd == 'yes') {
        $sql .= " AND s.fk_multicurrency = 2"; // USD
    } else {
        $sql .= " AND s.fk_multicurrency = 1"; // CAD
    }

    $sql .= " HAVING balance_due <> 0";

    $resql = $db->query($sql);
    if ($resql) {
        while ($obj = $db->fetch_object($resql)) {
            $customer_id = $obj->customer_id;
            $balance_due = $obj->balance_due;

            // Skip if balance due is effectively zero
            if (abs($balance_due) < 0.01) continue;

            // Get validation date with fallback
            $validation_date = $obj->date_valid;
            if (empty($validation_date)) {
                $validation_date = $obj->invoice_date;
            }
            
            // Convert to timestamp
            $validation_timestamp = strtotime($validation_date);
            if ($validation_timestamp === false) {
                $validation_timestamp = time();
            }
            
            // Calculate days overdue
            $days_overdue = floor(($filter_date - $validation_timestamp) / (60 * 60 * 24));
            
            // Determine aging period
            $aging_period = get_aging_period($days_overdue);
            
            // Initialize customer data if not exists
            if (!isset($sorted_customers[$customer_id])) {
                $sorted_customers[$customer_id] = [
                    'customer_id' => $customer_id,
                    'customer_name' => $obj->customer_name,
                    'customer_code' => $obj->code_client,
                    'payment_term' => $obj->customer_payment_term,
                    'Future' => 0,
                    'Current' => 0,
                    '30 Days' => 0,
                    '60 Days' => 0,
                    '90 Days' => 0,
                    '120 Days+' => 0,
                    'total' => 0,
                    'total_excl_future' => 0,
                    'invoices' => []
                ];
            }

            // Add invoice to customer's invoice list
            $sorted_customers[$customer_id]['invoices'][] = [
                'invoice_id' => $obj->invoice_id,
                'invoice_ref' => $obj->invoice_ref,
                'invoice_date' => $obj->invoice_date,
                'validation_date' => date('Y-m-d', $validation_timestamp),
                'balance_due' => $balance_due,
                'aging_period' => $aging_period
            ];
            
            // Update customer totals
            $sorted_customers[$customer_id][$aging_period] += $balance_due;
            $sorted_customers[$customer_id]['total'] += $balance_due;
            if ($aging_period != 'Future') {
                $sorted_customers[$customer_id]['total_excl_future'] += $balance_due;
            }

            // Update grand totals
            if (!isset($total_aging[$aging_period])) {
                $total_aging[$aging_period] = 0;
            }
            $total_aging[$aging_period] += $balance_due;
            if (!isset($total_aging['total'])) {
                $total_aging['total'] = 0;
            }
            $total_aging['total'] += $balance_due;
            if ($aging_period != 'Future') {
                if (!isset($total_aging['total_excl_future'])) {
                    $total_aging['total_excl_future'] = 0;
                }
                $total_aging['total_excl_future'] += $balance_due;
            }
        }
        
        // Sort customers
        if (!empty($sorted_customers)) {
            if ($sort_field == 'total') {
                usort($sorted_customers, function($a, $b) use ($sort_order) {
                    if ($sort_order == 'asc') {
                        return $a['total'] - $b['total'];
                    } else {
                        return $b['total'] - $a['total'];
                    }
                });
            } else {
                // Default sort by customer name
                usort($sorted_customers, function($a, $b) use ($sort_order) {
                    if ($sort_order == 'asc') {
                        return strcasecmp($a['customer_name'], $b['customer_name']);
                    } else {
                        return strcasecmp($b['customer_name'], $a['customer_name']);
                    }
                });
            }
        }
    }
}
// Process the query results if licensed
if ($is_licensed && isset($resql) && $resql) {
    while ($obj = $db->fetch_object($resql)) {
        $customer_id = $obj->customer_id;
        $customer_name = $obj->customer_name;
        $customer_code = $obj->code_client;
        $payment_term_id = $obj->payment_term;
        $customer_payment_term_id = $obj->customer_payment_term; // The customer's default payment term
        $balance_due = $obj->balance_due;
        $invoice_date = $obj->invoice_date;
        $invoice_id = $obj->invoice_id;
        $invoice_ref = $obj->invoice_ref;
        
        // Get validation date
        $validation_date = $obj->date_valid;
        if (empty($validation_date)) {
            // Fallback to invoice date if validation date is not available
            $validation_date = $obj->invoice_date;
        }
        
        // Convert validation date to timestamp for calculations
        $validation_timestamp = strtotime($validation_date);
        if ($validation_timestamp === false) {
            $validation_timestamp = time(); // Fallback if conversion fails
        }
        
        // Store the validation date in a format that can be displayed
        $formatted_validation_date = date('Y-m-d', $validation_timestamp);
        
        // Determine aging period based on validation date
        $days_overdue = floor(($filter_date - $validation_timestamp) / (60 * 60 * 24));
        $aging_period = get_aging_period($days_overdue);
        
        // Store invoice data in customer array
        if (!isset($sorted_customers[$customer_id])) {
            $sorted_customers[$customer_id] = [
                'customer_id' => $customer_id,
                'customer_name' => $customer_name,
                'customer_code' => $customer_code,
                'payment_term' => isset($paymentTermsMapping[$customer_payment_term_id]) ? 
                    $paymentTermsMapping[$customer_payment_term_id]['label'] : 'Unknown',
                'invoices' => [],
                'Future' => 0, 'Current' => 0, '30 Days' => 0, '60 Days' => 0, '90 Days' => 0, '120 Days+' => 0, 'total' => 0, 'total_excl_future' => 0
            ];
        }
        
        // Add invoice to customer's invoice list
        $sorted_customers[$customer_id]['invoices'][] = [
            'invoice_id' => $invoice_id,
            'invoice_ref' => $invoice_ref,
            'invoice_date' => $invoice_date,
            'validation_date' => $formatted_validation_date,
            'balance_due' => $balance_due,
            'aging_period' => $aging_period
        ];
        
        // Update customer totals
        $sorted_customers[$customer_id][$aging_period] += $balance_due;
        $sorted_customers[$customer_id]['total'] += $balance_due;
        if ($aging_period != 'Future') {
            $sorted_customers[$customer_id]['total_excl_future'] += $balance_due;
        }
        
        // Update grand totals
        $total_aging[$aging_period] += $balance_due;
        $total_aging['total'] += $balance_due;
        if ($aging_period != 'Future') {
            $total_aging['total_excl_future'] += $balance_due;
        }
    }
    
    // Sort customers alphabetically
    usort($sorted_customers, function($a, $b) {
        return strcmp($a['customer_name'], $b['customer_name']);
    });
} // End of if ($is_licensed && isset($resql) && $resql)

// License check was moved before the SQL query

// Output the report
// Page title
$license_indicator = ''; // Hide the license indicator

$title = 'Customer Aging Search Report';
llxHeader('', $title);

print_fiche_titre('Customer Aging Search Report ' . $license_indicator, '', 'bill', 0, 'left');

// Add custom CSS for license status
echo '<style>' . file_get_contents(__DIR__ . '/../License/license_styles.css') . '</style>';

?>

<style>
/* Filter styles */
.filter-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    align-items: flex-end;
}
.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}
.filter-group label {
    margin-bottom: 5px;
    font-weight: bold;
}
.filter-group select, .filter-group input {
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.checkbox-group {
    display: flex;
    align-items: center;
    margin-top: 10px;
}
.checkbox-group label {
    margin-left: 5px;
    font-weight: normal;
}
.submit-button {
    padding: 6px 2px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    width: auto;
    min-width: 0;
    max-width: none;
    white-space: nowrap;
}
.submit-button:hover {
    background-color: #45a049;
}

/* Table styles */
table { border-collapse: collapse; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; }
th { background-color: #f2f2f2; text-align: right; } /* Right align headers */
th.customer-name, th.text-left { text-align: left; } /* Left align Customer Name header */
td { text-align: right; } /* Right align values */
td.text-left { text-align: left; }
.group-header { background-color: #ddd; font-weight: bold; }
.customer-name { width: 25%; text-align: left; }
.text-left { text-align: left; width: 7.5%; } /* Style for code column, adjusted from 10% (0.75 * 10%) */
.payment-term-header { background-color: #eee; font-weight: bold; }
/* Main table: 6 amount columns in header, customer data rows, and 'Total for filtered' row */
table > thead > tr:not(.search-row) > th:not(.text-left):not(.customer-name),
table > tbody > tr.clickable > td:not(.text-left):not(.customer-name),
table > tbody > tr.group-header > td:not(.text-left):not(.customer-name) {
    width: 11.16%;
}

/* Nested table for details */
.nested-table { width: 100%; margin-top: 5px; margin-bottom: 5px; border: 1px solid #e0e0e0; }
.nested-table th, .nested-table td { padding: 5px; }
.nested-table th { background-color: #f0f0f0; }

/* Specific widths for Invoice Ref and Validation Date in the nested table */
.nested-table th:nth-child(1).text-left,
.nested-table td:nth-child(1).text-left { /* Invoice Ref column */
    width: 12%;
}

.nested-table th:nth-child(2).text-left,
.nested-table td:nth-child(2).text-left { /* Validation Date column */
    width: 21%;
}

/* Width for the 6 amount columns in the nested table */
.nested-table th:not(.text-left),
.nested-table td:not(.text-left) {
    width: 11.16%; /* (100 - 12 - 21) / 6 */
}


th { white-space: normal; width: auto; } /* Allow headers to wrap */
.search-input {
    width: 95%;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 5px;
}
.search-row { background-color: #f8f8f8; }
/* Hover effect for customer rows */
tbody tr:not(.group-header):not(.spacing-row):not([style*="display: none"]):hover {
    background-color: #f0f0f0;
}
/* Add expand indicator styles */
.clickable td:first-child {
    position: relative;
    padding-left: 20px; /* Make space for the indicator */
}

.clickable td:first-child::before {
    content: "▶";
    position: absolute;
    left: 5px;
    transition: transform 0.2s;
    font-size: 10px;
    color: #555;
}

.expanded td:first-child::before {
    transform: rotate(90deg);
    color: #2b4570; /* About button blue color for expanded indicator */
}

/* Style for expanded customer rows */
.expanded {
    background-color: #e0e5f0 !important; /* Lighter shade of the About button blue */
}

.expanded td {
    border-bottom: 2px solid #2b4570 !important; /* About button blue for border */
}

/* Style for detail rows */
.detail-row td {
    background-color: #f9f9f9;
    padding: 15px;
}

/* About button styling */
.about-button {
    padding: 6px 15px;
    background-color: #2b4570;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: bold;
}
.about-button:hover {
    background-color: #1a2a43;
}

/* Payment term container styling */
.payment-term-container {
    display: flex;
    gap: 15px;
}
.payment-term-container .filter-group {
    min-width: 150px;
}
.checkbox-align {
    margin-top: 24px; /* Aligns checkbox with dropdown */
    display: flex;
    align-items: center;
}
.checkbox-align label {
    margin-right: 5px; /* Space between label and checkbox */
}
</style>

<script type="text/javascript">
// Function to toggle detail view when clicking on a customer row
function toggleDetail(row) {
    // Find the next row (detail row)
    var detailRow = row.nextElementSibling;

    // Check if it's a detail row
    if (detailRow && detailRow.querySelector('td[colspan]')) {
        // Get computed style to check actual display state
        var computedStyle = window.getComputedStyle(detailRow);
        var isHidden = computedStyle.display === 'none';

        // Toggle visibility
        if (isHidden) {
            detailRow.style.display = 'table-row';
            // Mark as expanded
            row.classList.add('expanded');
            var detailTable = detailRow.querySelector('table.nested-table');
            if (detailTable) {
                detailTable.setAttribute('data-expanded', 'true');
            }
        } else {
            detailRow.style.display = 'none';
            // Mark as collapsed
            row.classList.remove('expanded');
            var detailTable = detailRow.querySelector('table.nested-table');
            if (detailTable) {
                detailTable.removeAttribute('data-expanded');
            }
        }
    }
}

// Initialize the table when the page loads
function initTable() {
    // Get all customer rows
    var customerRows = document.querySelectorAll('table > tbody > tr.clickable');

    // Get all detail rows
    var detailRows = document.querySelectorAll('table > tbody > tr.detail-row');

    // Hide all detail rows initially
    for (var i = 0; i < detailRows.length; i++) {
        detailRows[i].style.display = 'none';
    }

    // Remove expanded class from all customer rows
    for (var j = 0; j < customerRows.length; j++) {
        customerRows[j].classList.remove('expanded');
    }
    
    // Remove any data-expanded attributes from nested tables
    var detailTables = document.querySelectorAll('table.nested-table');
    for (var k = 0; k < detailTables.length; k++) {
        detailTables[k].removeAttribute('data-expanded');
    }
}

// Call initTable when the page loads
window.onload = function() {
    // Hide all details immediately before any other processing
    var detailRows = document.querySelectorAll('table > tbody > tr.detail-row');
    for (var i = 0; i < detailRows.length; i++) {
        detailRows[i].style.display = 'none';
    }
    
    // Then run the full initialization
    initTable();
};

function filterTable() {
    // Get search values
    var codeSearch = document.getElementById('codeSearch').value.toLowerCase();
    var nameSearch = document.getElementById('nameSearch').value.toLowerCase();

    // Get customer and detail rows
    var customerRows = document.querySelectorAll('table > tbody > tr.clickable');
    var detailRows = document.querySelectorAll('table > tbody > tr.detail-row');
    var matchedCustomerCount = 0;

    // First pass: process customer rows
    for (var i = 0; i < customerRows.length; i++) {
        var row = customerRows[i];

        // Check if it matches search criteria
        var codeCell = row.cells[0];
        var nameCell = row.cells[1];

        // Get text content
        var code = codeCell ? codeCell.textContent.toLowerCase() : '';
        var name = nameCell ? nameCell.textContent.toLowerCase() : '';

        // Check if row matches search criteria
        var matchesCode = code.includes(codeSearch);
        var matchesName = name.includes(nameSearch);

        // Show row if both criteria match
        if (matchesCode && matchesName) {
            row.style.display = 'table-row';
            row.setAttribute('data-matched', 'true');
            matchedCustomerCount++;
        } else {
            row.style.display = 'none';
            row.setAttribute('data-matched', 'false');
        }
    }

    // Determine if we should auto-expand details
    var autoExpandDetails = matchedCustomerCount <= 5 && (codeSearch.length > 0 || nameSearch.length > 0);

    // Second pass: handle detail rows based on their parent customer row
    for (var j = 0; j < detailRows.length; j++) {
        var detailRow = detailRows[j];
        var prevRow = detailRow.previousElementSibling;

        // Skip if previous row is not a customer row
        if (!prevRow || !prevRow.classList.contains('clickable')) {
            detailRow.style.display = 'none';
            continue;
        }

        // If customer row is visible, check if we should show details
        if (prevRow.getAttribute('data-matched') === 'true') {
            // Auto-expand if we have few results and search is active
            if (autoExpandDetails) {
                detailRow.style.display = 'table-row';
                prevRow.classList.add('expanded');
                // Mark as expanded
                var detailTable = detailRow.querySelector('table.nested-table');
                if (detailTable) {
                    detailTable.setAttribute('data-expanded', 'true');
                }
            } else {
                // Check if the detail row was already expanded by the user
                var detailTable = detailRow.querySelector('table.nested-table');
                if (detailTable && detailTable.hasAttribute('data-expanded')) {
                    detailRow.style.display = 'table-row';
                    prevRow.classList.add('expanded');
                } else {
                    detailRow.style.display = 'none';
                    prevRow.classList.remove('expanded');
                }
            }
        } else {
            // Customer row is hidden, so hide detail row too
            detailRow.style.display = 'none';
            prevRow.classList.remove('expanded');
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const warehouseSelect = document.getElementById('filter_warehouse');
    const customerDropdown = document.getElementById('customer');

    function updateCustomerDropdown(warehouseId) {
        if (!customerDropdown) return; // Should not happen if HTML is correct

        // Show a loading state
        customerDropdown.innerHTML = '<option value="all">Loading customers...</option>';
        customerDropdown.disabled = true;

        fetch(`<?php echo $_SERVER['PHP_SELF']; ?>?action=fetch_customers&warehouse_id=${encodeURIComponent(warehouseId)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                customerDropdown.innerHTML = ''; // Clear existing options
                
                // Add "All Customers" option
                let allCustomersOption = new Option('All Customers', 'all');
                customerDropdown.add(allCustomersOption);

                // Populate with new customers
                data.forEach(customer => {
                    let option = new Option(customer.name, customer.id); // Name is already escaped from server
                    customerDropdown.add(option);
                });
                
                customerDropdown.disabled = false;
                customerDropdown.value = 'all'; // Set default selection to "All Customers"
            })
            .catch(error => {
                console.error('Error fetching customers:', error);
                customerDropdown.innerHTML = '<option value="all">Error loading customers</option>';
                customerDropdown.disabled = false; // Re-enable, or decide on error state
            });
    }

    if (warehouseSelect) {
        warehouseSelect.addEventListener('change', function() {
            updateCustomerDropdown(this.value);
        });
    }
});
</script>

<form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>" onsubmit="return handleFilterClick()">
<input type="hidden" name="token" value="<?php echo newToken(); ?>">
<div class="filter-container">
    <div class="filter-group">
        <label for="customer">Customer:</label>
        <select name="customer" id="customer">
            <option value="all" <?php echo ($selected_customer == 'all') ? 'selected' : ''; ?>>All Customers</option>
            <?php foreach ($customer_options as $id => $name): ?>
                <option value="<?php echo $id; ?>" <?php echo ($selected_customer == $id) ? 'selected' : ''; ?>><?php echo $name; ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="filter-group">
        <label for="filter_month">Month:</label>
        <select name="filter_month" id="filter_month">
            <?php for ($m = 1; $m <= 12; $m++): ?>
                <option value="<?php echo $m; ?>" <?php echo ($selected_month == $m) ? 'selected' : ''; ?>><?php echo date('F', mktime(0, 0, 0, $m, 1, 2000)); ?></option>
            <?php endfor; ?>
        </select>
    </div>
    <div class="filter-group">
        <label for="filter_year">Year:</label>
        <select name="filter_year" id="filter_year">
            <?php for ($y = date('Y') - 2; $y <= date('Y') + 3; $y++): ?>
                <option value="<?php echo $y; ?>" <?php echo ($selected_year == $y) ? 'selected' : ''; ?>><?php echo $y; ?></option>
            <?php endfor; ?>
        </select>
    </div>
    <div class="payment-term-container">
        <!-- Hide Payment Term dropdown and checkbox
        <div class="filter-group">
            <label for="payment_term">Payment Term:</label>
            <select name="payment_term" id="payment_term">
                <option value="all" <?php echo ($selected_payment_term == 'all') ? 'selected' : ''; ?>>All Payment Terms</option>
                <?php foreach ($payment_term_options as $id => $name): ?>
                    <option value="<?php echo $id; ?>" <?php echo ($selected_payment_term == $id) ? 'selected' : ''; ?>><?php echo $name; ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="checkbox-align">
            <label for="show_payment_term">Show Payment Term</label>
            <input type="checkbox" name="show_payment_term" id="show_payment_term" value="yes" <?php echo ($show_payment_term == 'yes') ? 'checked' : ''; ?> onchange="this.form.submit()">
        </div>
        -->
        <!-- Keep only the Show Total Aging checkbox -->
        <div class="checkbox-align">
            <label for="show_total_aging">Show Total Aging</label>
            <input type="checkbox" name="show_total_aging" id="show_total_aging" value="yes" <?php echo ($show_total_aging == 'yes') ? 'checked' : ''; ?> onchange="this.form.submit()">
        </div>
    </div>
    <div class="checkbox-align">
        <label for="filter_currency_usd">Show USD (Unchecked for CAD)</label>
        <input type="checkbox" name="filter_currency_usd" id="filter_currency_usd" value="yes" <?php echo ($selected_currency_usd == 'yes') ? 'checked' : ''; ?> onchange="this.form.submit()">
    </div>
    <div class="filter-group">
        <input type="submit" class="button" name="filter" value="Filter">
    </div>
    <div class="filter-group">
        <button type="button" class="about-button" onclick="window.open('about_customer_aging_search.php', '_blank', 'width=900,height=800,scrollbars=yes,resizable=yes,status=no,location=no,toolbar=no,menubar=no,fullscreen=yes,maximized=yes');">About Report</button>
    </div>
</div>
</form>

<?php
// Display license status message
// echo $license_status_message; // Commented out to hide the detailed license message

// Only show report data if licensed
if (!$is_licensed): ?>
<div class="info">Report data is not available for unlicensed months.</div>
<?php elseif (empty($sorted_customers)): ?>
<div class="info">No data available.</div>
<?php else: ?>
<table>
<thead>
<tr class="search-row">
<th class="text-left"><input type="text" id="codeSearch" class="search-input" placeholder="Search Code..." onkeyup="filterTable()"></th>
<th class="customer-name"><input type="text" id="nameSearch" class="search-input" placeholder="Search Customer Name..." onkeyup="filterTable()"></th>
<?php if ($show_payment_term == 'yes'): ?>
<th class="text-left"></th>
<?php endif; ?>
<th></th>
<th></th>
<th></th>
<th></th>
<th></th>
<th></th>
<th></th>
</tr>
<tr>
<th class="text-left">Code</th>
<th class="customer-name">Customer Name</th>
<?php if ($show_payment_term == 'yes'): ?>
<th class="text-left">Payment Term</th>
<?php endif; ?>
<th>120 Days+</th>
<th>90 Days</th>
<th>60 Days</th>
<th>30 Days</th>
<th>Current<br>(<?php echo date('M Y', $filter_date); ?>)</th>
<th>Total Due</th>
<!-- Hidden columns
<th><strong>Total<br>Excl Future</strong></th>
<th>Future</th>
<th>Total Incl Future</th>
-->
</tr>
</thead>
<tbody>
<?php if ($show_total_aging == 'yes'): ?>
<tr class="group-header">
<td class="text-left"></td>
<td class="customer-name">Total for selected Customers:</td>
<?php if ($show_payment_term == 'yes'): ?>
<td></td>
<?php endif; ?>
<td><?php echo number_format($total_aging['120 Days+'], 2, '.', ','); ?></td>
<td><?php echo number_format($total_aging['90 Days'], 2, '.', ','); ?></td>
<td><?php echo number_format($total_aging['60 Days'], 2, '.', ','); ?></td>
<td><?php echo number_format($total_aging['30 Days'], 2, '.', ','); ?></td>
<td><?php echo number_format($total_aging['Current'], 2, '.', ','); ?></td>
<td><strong><?php echo number_format($total_aging['total_excl_future'], 2, '.', ','); ?></strong></td>
<!-- Hidden columns
<td><strong><?php echo number_format($total_aging['total_excl_future'], 2, '.', ','); ?></strong></td>
<td><?php echo number_format($total_aging['Future'], 2, '.', ','); ?></td>
<td><?php echo number_format($total_aging['total'], 2, '.', ','); ?></td>
-->
</tr>
<!-- Add spacing row -->
<tr class="spacing-row"><td colspan="<?php echo ($show_payment_term == 'yes') ? '11' : '10'; ?>">&nbsp;</td></tr>
<?php endif; ?>

<?php
// Loop through customers without grouping by payment term
foreach ($sorted_customers as $customer_data): ?>
<tr onclick="toggleDetail(this)" class="clickable">
<td class="text-left"><?php echo $customer_data['customer_code']; ?></td>
<td class="customer-name"><?php echo $customer_data['customer_name']; ?></td>
<?php if ($show_payment_term == 'yes'): ?>
<td class="text-left"><?php echo $customer_data['payment_term']; ?></td>
<?php endif; ?>
<td><?php echo number_format($customer_data['120 Days+'], 2, '.', ','); ?></td>
<td><?php echo number_format($customer_data['90 Days'], 2, '.', ','); ?></td>
<td><?php echo number_format($customer_data['60 Days'], 2, '.', ','); ?></td>
<td><?php echo number_format($customer_data['30 Days'], 2, '.', ','); ?></td>
<td><?php echo number_format($customer_data['Current'], 2, '.', ','); ?></td>
<td><strong><?php echo number_format($customer_data['total_excl_future'], 2, '.', ','); ?></strong></td>
<!-- Hidden columns
<td><strong><?php echo number_format($customer_data['total_excl_future'], 2, '.', ','); ?></strong></td>
<td><?php echo number_format($customer_data['Future'], 2, '.', ','); ?></td>
<td><?php echo number_format($customer_data['total'], 2, '.', ','); ?></td>
-->


<?php if ($show_detail == 'yes' && !empty($customer_data['invoices'])): ?>
<?php usort($customer_data['invoices'], function($a, $b) {
    return strtotime($a['validation_date']) - strtotime($b['validation_date']);
}); ?>
<tr class="detail-row">
<td colspan="<?php echo ($show_payment_term == 'yes') ? '9' : '8'; ?>">
<table class="nested-table">
<thead>
<tr>
<th class="text-left">Invoice Ref</th>
<th class="text-left">Validation Date</th>
<th>120 Days+</th>
<th>90 Days</th>
<th>60 Days</th>
<th>30 Days</th>
<th>Current</th>
<th>Total Due</th>
<!-- Hidden columns
<th><strong>Total<br>Excl Future</strong></th>
<th>Future</th>
<th>Total Incl Future</th>
-->
</tr>
</thead>
<tbody>
<?php foreach ($customer_data['invoices'] as $invoice): ?>
<tr>
<td class="text-left">
    <a href="<?php echo DOL_URL_ROOT; ?>/compta/facture/card.php?facid=<?php echo $invoice['invoice_id']; ?>" target="_blank">
        <?php echo $invoice['invoice_ref']; ?>
    </a>
</td>
<td class="text-left"><?php echo isset($invoice['validation_date']) ? $invoice['validation_date'] : 'N/A'; ?></td>
<td><?php echo number_format(($invoice['aging_period'] == '120 Days+') ? $invoice['balance_due'] : 0, 2, '.', ','); ?></td>
<td><?php echo number_format(($invoice['aging_period'] == '90 Days') ? $invoice['balance_due'] : 0, 2, '.', ','); ?></td>
<td><?php echo number_format(($invoice['aging_period'] == '60 Days') ? $invoice['balance_due'] : 0, 2, '.', ','); ?></td>
<td><?php echo number_format(($invoice['aging_period'] == '30 Days') ? $invoice['balance_due'] : 0, 2, '.', ','); ?></td>
<td><?php echo number_format(($invoice['aging_period'] == 'Current') ? $invoice['balance_due'] : 0, 2, '.', ','); ?></td>
<td><strong><?php echo number_format(($invoice['aging_period'] != 'Future') ? $invoice['balance_due'] : 0, 2, '.', ','); ?></strong></td>
<!-- Hidden columns
<td><strong><?php echo number_format(($invoice['aging_period'] != 'Future') ? $invoice['balance_due'] : 0, 2, '.', ','); ?></strong></td>
<td><?php echo number_format(($invoice['aging_period'] == 'Future') ? $invoice['balance_due'] : 0, 2, '.', ','); ?></td>
<td><?php echo number_format($invoice['balance_due'], 2, '.', ','); ?></td>
-->
</tr>
<?php endforeach; ?>

<!-- Add a blank row at the end of each customer's detail -->
<tr class="blank-row">
<td colspan="11" style="height: 10px; background-color: #f9f9f9; border: none;"></td>
</tr>
</tbody>
</table>
</td>
</tr>
<?php endif; ?>
<?php endforeach; ?>
</tbody>

<?php endif; ?>

<?php
llxFooter();
$db->close();
?>
