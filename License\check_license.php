<?php
// Load the license validation functions
require_once __DIR__ . '/license_validation.php';

// Get the month and year from the query string or use current month/year
$month = isset($_GET['month']) ? (int)$_GET['month'] : (int)date('n');
$month_str = str_pad($month, 2, '0', STR_PAD_LEFT);

$year = isset($_GET['year']) ? (int)$_GET['year'] : (int)date('Y');
$year_str = (string)$year;

$date_str = "$year_str-$month_str";
$month_name = date('F', mktime(0, 0, 0, $month, 1, $year));

// Check if the license is valid
$is_licensed = is_month_licensed($month, $year);

// Get the license status message
$license_message = get_license_status_message($month, $year);

// HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>License Check for <?php echo "$month_name $year"; ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .license-valid {
            background-color: #dff0d8;
            border: 1px solid #d6e9c6;
            color: #3c763d;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .license-invalid {
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            color: #a94442;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .form-group { margin-bottom: 15px; }
        label { display: inline-block; width: 80px; }
        select { padding: 5px; }
        button { padding: 5px 10px; background-color: #337ab7; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .file-info { margin-top: 20px; padding: 10px; background-color: #f8f8f8; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>License Check for <?php echo "$month_name $year"; ?></h1>
    
    <form method="get" action="">
        <div class="form-group">
            <label for="month">Month:</label>
            <select name="month" id="month">
                <?php for ($m = 1; $m <= 12; $m++): ?>
                <option value="<?php echo $m; ?>" <?php echo ($month == $m) ? 'selected' : ''; ?>>
                    <?php echo date('F', mktime(0, 0, 0, $m, 1)); ?>
                </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="year">Year:</label>
            <select name="year" id="year">
                <?php for ($y = date('Y') - 2; $y <= date('Y') + 3; $y++): ?>
                <option value="<?php echo $y; ?>" <?php echo ($year == $y) ? 'selected' : ''; ?>>
                    <?php echo $y; ?>
                </option>
                <?php endfor; ?>
            </select>
        </div>
        
        <button type="submit">Check License</button>
    </form>
    
    <div class="license-status">
        <?php echo $license_message; ?>
    </div>
    
    <?php
    // Display file information if it exists
    $license_file = __DIR__ . "/$date_str.lic";
    if (file_exists($license_file)):
    ?>
    <div class="file-info">
        <h2>License File Information</h2>
        <p><strong>File:</strong> <?php echo "$date_str.lic"; ?></p>
        <p><strong>Size:</strong> <?php echo filesize($license_file); ?> bytes</p>
        <p><strong>Created:</strong> <?php echo date('Y-m-d H:i:s', filemtime($license_file)); ?></p>
        <p><strong>Is Valid:</strong> <?php echo $is_licensed ? 'Yes' : 'No'; ?></p>
    </div>
    <?php endif; ?>
    
    <p><a href="test_validation.php">Run Full Validation Tests</a></p>
</body>
</html>
