<?php
// Load Dolibarr environment
$res = 0;
if (!$res && file_exists("../../main.inc.php")) $res = @include '../../main.inc.php';
if (!$res && file_exists("../../../main.inc.php")) $res = @include '../../../main.inc.php';
if (!$res) die("Include of main fails");
// Check user rights (optional, but good practice for reports)
// if (empty($user->rights->societe->lire)) accessforbidden();

// Get selected warehouse if form was submitted
$selected_warehouse = 'all';
$selected_customer = GETPOST('filter_customer', 'alpha') ?: 'all'; // To capture customer selection
$selected_month = GETPOST('filter_month', 'int') ?: date('n');      // To capture month selection
$selected_year = GETPOST('filter_year', 'int') ?: date('Y');        // To capture year selection
$show_detail_option = (GETPOST('show_detail_option', 'alpha') == 'yes') ? 'yes' : 'no'; // For Show Detail checkbox
$show_total_aging_option = (GETPOST('show_total_aging_option', 'alpha') == 'yes') ? 'yes' : 'no'; // For Show Total Aging checkbox
$selected_currency_usd = (GETPOST('filter_currency_usd', 'alpha') == 'yes') ? 'yes' : 'no'; // For USD checkbox

// Get sort parameters
$sort_field = GETPOST('sort_field', 'alpha') ?: 'customer_name'; // Default to customer_name, can also be 'total_due'
$sort_order = GETPOST('sort_order', 'alpha') ?: 'asc';         // Default to ascending

$warehouse_options = ['all' => 'All Branches']; // Default "All" option

// Initialize customer options and flag for showing the dropdown
$customer_options = ['all' => 'All Customers'];
$display_second_row_filters = false; // This flag will control visibility of the second filter row

// Determine if a filter or sort action is being performed that requires data processing/display
// 'filter=FILTER' in GET is used by sort links to trigger data refresh
$action_trigger = (isset($_POST['filter']) || isset($_POST['export_csv_totals']) || isset($_POST['export_csv_detail']) || (isset($_GET['filter']) && $_GET['filter'] == 'FILTER')); // Trigger data processing also on export

// Determine if the second row of filters should be displayed.
// This happens if "SELECT BRANCH" (POST) was clicked, or if "FILTER" (POST or GET via sort link) was triggered.
$display_second_row_filters = true;


// Populate customer options if the second row of filters is to be displayed
if ($display_second_row_filters) {
    $sql_customers = "SELECT s.rowid, s.nom FROM " . MAIN_DB_PREFIX . "societe as s WHERE s.client >= 1";
    // Debugging: Log the selected currency state
    dol_syslog("Customer Aging Report: selected_currency_usd = " . $selected_currency_usd, LOG_DEBUG);
    if ($selected_currency_usd == 'yes') {
        $sql_customers .= " AND s.fk_multicurrency = 2"; // USD
    } else {
        $sql_customers .= " AND s.fk_multicurrency = 1"; // CAD
    }
    dol_syslog("Customer Aging Report: SQL for customers = " . $sql_customers, LOG_DEBUG);
    // If a specific branch is selected (not 'all'), filter customers by that branch
    // Optional: Add entity check if multicompany is enabled and relevant for your setup
    // $sql_customers .= " AND s.entity IN (" . getEntity('societe') . ")";
    $sql_customers .= " ORDER BY s.nom ASC";

    $resql_customers = $db->query($sql_customers);
    if ($resql_customers) {
        while ($obj_customer = $db->fetch_object($resql_customers)) {
            $customer_options[(string)$obj_customer->rowid] = dol_escape_htmltag($obj_customer->nom); // Cast rowid to string for key
        }
    }
}

// --- Logic for fetching and processing aging data (adapted from customer_aging_search.php) ---
$report_data_display = [];
$total_aging_summary = [
    'Future'    => 0,
    'Current'   => 0,
    '30 Days'   => 0,
    '60 Days'   => 0,
    '90 Days'   => 0,
    '120 Days+' => 0,
    'total'     => 0,
    'total_excl_future' => 0
];

// Define aging periods (consistent with customer_aging_search.php)
$aging_periods_config = [
    'Future'    => [-9999, -1],
    'Current'   => [0, 29],
    '30 Days'   => [30, 59],
    '60 Days'   => [60, 89],
    '90 Days'   => [90, 119],
    '120 Days+' => [120, 9999]
];

function get_aging_period_label_by_days_ca($days_overdue) { // Renamed to avoid conflict if another exists
    global $aging_periods_config;
    foreach ($aging_periods_config as $label => $range) {
        if ($days_overdue >= $range[0] && $days_overdue <= $range[1]) {
            return $label;
        }
    }
    return 'Unknown'; // Fallback
}

// Fetch and process data if the second row of filters is displayed AND a filter/sort action was triggered
if ($display_second_row_filters && $action_trigger) {
    $filter_date_timestamp = mktime(23, 59, 59, $selected_month, date('t', mktime(0, 0, 0, $selected_month, 1, $selected_year)), $selected_year);

    // SQL adapted from customer_aging_search.php
    $sql_report = "SELECT s.rowid AS customer_id, s.nom AS customer_name, s.code_client, s.fk_multicurrency,
                        f.rowid AS invoice_id, f.ref AS invoice_ref,
                        (f.total_ttc - IFNULL(( SELECT SUM(pf.amount) FROM " . MAIN_DB_PREFIX . "paiement_facture AS pf WHERE pf.fk_facture = f.rowid ), 0) +
                         IFNULL(( SELECT SUM(cn.total_ttc) FROM " . MAIN_DB_PREFIX . "facture AS cn WHERE cn.fk_facture_source = f.rowid AND cn.type = 2 ), 0)
                        ) AS balance_due,
                        f.datef AS invoice_date, f.date_valid
                 FROM " . MAIN_DB_PREFIX . "societe AS s
                 JOIN " . MAIN_DB_PREFIX . "facture AS f ON s.rowid = f.fk_soc
                 WHERE f.type != 2 AND f.paye = 0 AND f.fk_statut > 0"; // Unpaid and validated invoices
    if ($selected_currency_usd == 'yes') {
        $sql_report .= " AND s.fk_multicurrency = 2"; // USD
    } else {
        $sql_report .= " AND s.fk_multicurrency = 1"; // CAD
    }
    dol_syslog("Customer Aging Report: SQL for report = " . $sql_report, LOG_DEBUG);

    if ($selected_customer != 'all' && is_numeric($selected_customer)) {
        $sql_report .= " AND s.rowid = " . intval($selected_customer);
    }
    $sql_report .= " HAVING balance_due != 0"; // Consider invoices with any balance (positive or negative from CNs)

    $resql_report = $db->query($sql_report);
    if ($resql_report) {
        $temp_customer_data = [];
        while ($obj = $db->fetch_object($resql_report)) {
            $customer_id_key = (string)$obj->customer_id;

            if (!isset($temp_customer_data[$customer_id_key])) {
                $temp_customer_data[$customer_id_key] = [
                    'customer_name' => dol_escape_htmltag($obj->customer_name),
                    'customer_code' => dol_escape_htmltag($obj->code_client), // Added for consistency
                    'Future'    => 0, 'Current'   => 0, '30 Days'   => 0,
                    '60 Days'   => 0, '90 Days'   => 0, '120 Days+' => 0,
                    'total'     => 0, 'total_excl_future' => 0,
                    'invoices'  => []
                ];
            }

            $balance_due = (float)$obj->balance_due;

            $validation_date_str = !empty($obj->date_valid) && $obj->date_valid != '0000-00-00 00:00:00' ? $obj->date_valid : $obj->invoice_date;
            $validation_timestamp = strtotime($validation_date_str);

            if ($validation_timestamp) {
                $days_overdue = floor(($filter_date_timestamp - $validation_timestamp) / (60 * 60 * 24));
                $aging_period_label = get_aging_period_label_by_days_ca($days_overdue);

                if ($aging_period_label !== 'Unknown') {
                    $temp_customer_data[$customer_id_key][$aging_period_label] += $balance_due;
                    $total_aging_summary[$aging_period_label] += $balance_due;
                }
                $temp_customer_data[$customer_id_key]['total'] += $balance_due;
                $total_aging_summary['total'] += $balance_due;
                if ($aging_period_label != 'Future') {
                    $temp_customer_data[$customer_id_key]['total_excl_future'] += $balance_due;
                    $total_aging_summary['total_excl_future'] += $balance_due;
                }

                // Store invoice details if "Show Detail" is on
                if ($show_detail_option == 'yes') {
                    $temp_customer_data[$customer_id_key]['invoices'][] = [
                        'invoice_id' => $obj->invoice_id, // Add invoice_id here
                        'invoice_ref' => dol_escape_htmltag($obj->invoice_ref),
                        'validation_date' => date('Y-m-d', $validation_timestamp),
                        'balance_due' => $balance_due,
                        'aging_period' => $aging_period_label
                    ];
                }
            }
        }
        $report_data_display = $temp_customer_data;

        // Sort data based on $sort_field and $sort_order
        if (!empty($report_data_display)) {
            uasort($report_data_display, function ($a, $b) use ($sort_field, $sort_order) {
                if ($sort_field == 'customer_name') {
                    $val_a = $a['customer_name'];
                    $val_b = $b['customer_name'];
                    return ($sort_order == 'asc') ? strcmp($val_a, $val_b) : strcmp($val_b, $val_a);
                } elseif ($sort_field == 'total_due') {
                    $val_a = (float)$a['total_excl_future']; // Ensure this key matches your data array
                    $val_b = (float)$b['total_excl_future']; // Ensure this key matches your data array
                    if ($val_a == $val_b) return 0;
                    return ($sort_order == 'asc') ? ($val_a < $val_b ? -1 : 1) : ($val_b < $val_a ? -1 : 1);
                }
                // Add other sortable fields here
                // Add other sortable fields here in the future if needed
                return 0; // Default: no change in order
            });
        }

        // --- CSV Export Logic ---
        if (isset($_POST['export_csv_totals'])) { // CSV Export for Totals/Summary
            $csv_filename = 'customer_aging_report';
            $csv_filename .= '_all_branches';

            if ($selected_customer != 'all' && isset($customer_options[$selected_customer])) {
                $csv_filename .= '_' . dol_sanitizeFileName(str_replace(' ', '_', $customer_options[$selected_customer]));
            } elseif ($selected_customer == 'all') {
                $csv_filename .= '_all_customers';
            }
            $csv_filename .= '_' . dol_sanitizeFileName(date('M', mktime(0, 0, 0, $selected_month, 1))) . '_' . $selected_year;
            $csv_filename .= '.csv';

            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $csv_filename . '"');
            $output = fopen('php://output', 'w');

            // Add UTF-8 BOM for Excel compatibility
            fputs($output, "\xEF\xBB\xBF");

            // Report Title and Filters
            fputcsv($output, array($langs->trans("CustomerAgingReport"))); // Create this translation key

            $branch_text_csv = $langs->trans("AllBranches");
            fputcsv($output, array($langs->trans("Branch") . ": " . $branch_text_csv));

            $customer_text_csv = ($selected_customer == 'all' || !isset($customer_options[$selected_customer]))
                ? $langs->trans("AllCustomers")
                : $customer_options[$selected_customer];
            fputcsv($output, array($langs->trans("Customer") . ": " . $customer_text_csv));

            fputcsv($output, array($langs->trans("Period") . ": " . $langs->trans(date('F', mktime(0, 0, 0, $selected_month, 1))) . " " . $selected_year));
            fputcsv($output, array("")); // Empty line

            // CSV Headers
            $csv_headers = [
                $langs->trans("CustomerName"),
                '120 Days+',
                '90 Days',
                '60 Days',
                '30 Days',
                'Current',
                $langs->trans("TotalDue")
            ];
            fputcsv($output, $csv_headers);

            // Total Aging Summary (if option is enabled)
            if ($show_total_aging_option == 'yes' && !empty($total_aging_summary)) {
                fputcsv($output, array(
                    $langs->trans("Totals"),
                    number_format((float)$total_aging_summary['120 Days+'], 2, '.', ','),
                    number_format((float)$total_aging_summary['90 Days'], 2, '.', ','),
                    number_format((float)$total_aging_summary['60 Days'], 2, '.', ','),
                    number_format((float)$total_aging_summary['30 Days'], 2, '.', ','),
                    number_format((float)$total_aging_summary['Current'], 2, '.', ','),
                    number_format((float)$total_aging_summary['total_excl_future'], 2, '.', ',')
                ));
            }

            // Report Data
            if (!empty($report_data_display)) {
                foreach ($report_data_display as $customer_id => $data) {
                    fputcsv($output, array(
                        $data['customer_name'], // Already HTML escaped, fputcsv handles quotes
                        number_format((float)$data['120 Days+'], 2, '.', ','),
                        number_format((float)$data['90 Days'], 2, '.', ','),
                        number_format((float)$data['60 Days'], 2, '.', ','),
                        number_format((float)$data['30 Days'], 2, '.', ','),
                        number_format((float)$data['Current'], 2, '.', ','),
                        number_format((float)$data['total_excl_future'], 2, '.', ',')
                    ));
                }
            } elseif (!($show_total_aging_option == 'yes' && !empty($total_aging_summary))) {
                fputcsv($output, array($langs->trans("NoDataFound")));
            }

            fclose($output);
            $db->close(); // Close DB connection as we are exiting
            exit;
        } elseif (isset($_POST['export_csv_detail']) && $show_detail_option == 'yes') { // CSV Export for Detail
            $csv_filename = 'customer_aging_detail_report';
            $csv_filename .= '_all_branches';

            if ($selected_customer != 'all' && isset($customer_options[$selected_customer])) {
                $csv_filename .= '_' . dol_sanitizeFileName(str_replace(' ', '_', $customer_options[$selected_customer]));
            } elseif ($selected_customer == 'all') {
                $csv_filename .= '_all_customers';
            }
            $csv_filename .= '_' . dol_sanitizeFileName(date('M', mktime(0, 0, 0, $selected_month, 1))) . '_' . $selected_year;
            $csv_filename .= '.csv';

            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $csv_filename . '"');
            $output = fopen('php://output', 'w');

            fputs($output, "\xEF\xBB\xBF"); // UTF-8 BOM

            // Report Title and Filters
            fputcsv($output, array($langs->trans("CustomerAgingDetailReport"))); // Create this translation key

            $branch_text_csv = $langs->trans("AllBranches");
            fputcsv($output, array($langs->trans("Branch") . ": " . $branch_text_csv));

            $customer_text_csv = ($selected_customer == 'all' || !isset($customer_options[$selected_customer]))
                ? $langs->trans("AllCustomers")
                : $customer_options[$selected_customer];
            fputcsv($output, array($langs->trans("Customer") . ": " . $customer_text_csv));
            fputcsv($output, array($langs->trans("Period") . ": " . $langs->trans(date('F', mktime(0, 0, 0, $selected_month, 1))) . " " . $selected_year));
            fputcsv($output, array("")); // Empty line

            // CSV Headers for Detail
            $csv_headers_detail = [
                $langs->trans("CustomerName"),
                $langs->trans("InvoiceRef"),
                $langs->trans("ValidationDate"),
                '120 Days+',
                '90 Days',
                '60 Days',
                '30 Days',
                'Current',
                $langs->trans("TotalDue") // For the invoice
            ];
            fputcsv($output, $csv_headers_detail);

            // Report Data (Detail)
            if (!empty($report_data_display)) {
                foreach ($report_data_display as $customer_id => $cust_data) {
                    if (!empty($cust_data['invoices'])) {
                        foreach ($cust_data['invoices'] as $invoice) {
                            fputcsv($output, array(
                                $cust_data['customer_name'],
                                $invoice['invoice_ref'],
                                $invoice['validation_date'],
                                number_format(($invoice['aging_period'] == '120 Days+') ? (float)$invoice['balance_due'] : 0, 2, '.', ','),
                                number_format(($invoice['aging_period'] == '90 Days') ? (float)$invoice['balance_due'] : 0, 2, '.', ','),
                                number_format(($invoice['aging_period'] == '60 Days') ? (float)$invoice['balance_due'] : 0, 2, '.', ','),
                                number_format(($invoice['aging_period'] == '30 Days') ? (float)$invoice['balance_due'] : 0, 2, '.', ','),
                                number_format(($invoice['aging_period'] == 'Current') ? (float)$invoice['balance_due'] : 0, 2, '.', ','),
                                number_format((float)$invoice['balance_due'], 2, '.', ',')
                            ));
                        }
                    }
                }
            } else {
                fputcsv($output, array($langs->trans("NoDataFound")));
            }
            fclose($output);
            $db->close();
            exit;
        }
    }
}

// Output the report
// Page title
$title = "Customer Aging Report";
llxHeader('', $title);
// Display the title. The license indicator is already an empty string.
print_fiche_titre($title, '', 'bill', 0, 'left');

// Output the Branch dropdown
//echo '<div class="fichecenter">'; // Removed for full width
//echo '<div class="fichehalfleft">'; // Removed for full width
echo '<form method="POST" action="'.$_SERVER['PHP_SELF'].'">';
echo '<input type="hidden" name="token" value="'.newToken().'">';


// Customer Dropdown - shown below the SELECT BRANCH button if a specific branch is selected
if ($display_second_row_filters) {
    echo '<div style="margin-top: 10px; display: flex; flex-wrap: wrap; gap: 15px; align-items: flex-end;">'; // Use flex for alignment


    // Customer Section
    echo '<div>';
    echo '<label for="filter_customer">Customer:</label><br>';
    echo $form->selectarray('filter_customer', $customer_options, $selected_customer, 0, 0, 0, '', 0, 0, 0, '', 'maxwidth200');
    echo '</div>';

    // Month Section
    echo '<div>';
    echo '<label for="filter_month">Month:</label><br>';
    echo '<select name="filter_month" id="filter_month" class="flat maxwidth150">';
    for ($m = 1; $m <= 12; $m++) {
        echo '<option value="'.$m.'" '.($selected_month == $m ? 'selected="selected"' : '').'>'.dol_escape_htmltag(date('F', mktime(0, 0, 0, $m, 1))).'</option>';
    }
    echo '</select>';
    echo '</div>';

    // Year Section
    echo '<div>';
    echo '<label for="filter_year">Year:</label><br>';
    echo '<select name="filter_year" id="filter_year" class="flat maxwidth100">';
    $current_year_for_loop = date('Y');
    for ($y = $current_year_for_loop - 5; $y <= $current_year_for_loop; $y++) { // Example range: last 5 years up to current year
        echo '<option value="'.$y.'" '.($selected_year == $y ? 'selected="selected"' : '').'>'.$y.'</option>';
    }
    echo '</select>';
    echo '</div>';

    // Options Section
    echo '<div>';
    echo '<label>Options:</label><br>'; // Label for the options group
    echo '<div class="checkbox-options" style="padding-top: 5px;">'; // Add some padding for alignment with dropdowns
        echo '<div class="checkbox-item" style="margin-bottom: 5px;">';
            echo '<input type="checkbox" name="filter_currency_usd" id="filter_currency_usd" value="yes" '.($selected_currency_usd == 'yes' ? 'checked="checked"' : '').'>';
            echo '<label for="filter_currency_usd" style="margin-left: 5px; font-weight: normal;">Show USD (else CAD)</label>';
        echo '</div>';
        echo '<div class="checkbox-item" style="margin-bottom: 5px;">';
            echo '<input type="checkbox" name="show_detail_option" id="show_detail_option" value="yes" '.($show_detail_option == 'yes' ? 'checked="checked"' : '').'>';
            echo '<label for="show_detail_option" style="margin-left: 5px; font-weight: normal;">Show Detail</label>';
        echo '</div>';
        echo '<div class="checkbox-item">';
            echo '<input type="checkbox" name="show_total_aging_option" id="show_total_aging_option" value="yes" '.($show_total_aging_option == 'yes' ? 'checked="checked"' : '').'>';
            echo '<label for="show_total_aging_option" style="margin-left: 5px; font-weight: normal;">Show Total Aging</label>';
        echo '</div>';
    echo '</div>';
    echo '</div>';

    // FILTER Button Section - aligned with other items in the flex container
    echo '<div>';
    // The label is empty to help with flex-end alignment if other items have multi-line labels or taller inputs.
    echo '<label>&nbsp;</label><br>'; 
    echo '<input type="submit" class="button" name="filter" value="'.$langs->trans("Filter").'">';
echo ' <input type="submit" class="button" name="export_csv_totals" value="'.$langs->trans("Excel_Total_Export").'" style="margin-left: 5px;">'; // Renamed value for clarity
echo ' <input type="submit" class="button" name="export_csv_detail" value="'.$langs->trans("Excel_Detail_Export").'" style="margin-left: 5px;" '.($show_detail_option != 'yes' ? 'disabled title="'.$langs->trans("EnableShowDetailToExportDetail").'"' : '').'>';
    echo '</div>';

    echo '</div>';
}

echo '</form>';

// Display all selected values below the form for confirmation
echo '<div style="margin-top:15px; padding-left:10px; margin-bottom: 20px;">'; // Added padding and bottom margin
// The "Selected Branch" text is now displayed inline with the branch selector itself.

if ($display_second_row_filters) { 
    // Display Selected Customer
    if ($selected_customer != 'all' && isset($customer_options[$selected_customer])) {
        //echo "<p>Selected Customer: " . dol_escape_htmltag($customer_options[$selected_customer]) . "</p>"; // Hidden as per plan.md
    }
    // Display Selected Period
    //echo "<p>Selected Period: " . dol_escape_htmltag(date('F', mktime(0, 0, 0, $selected_month, 1))) . " " . $selected_year . "</p>"; // Hidden as per plan.md
    // Display selected options
    //echo "<p>Show Detail: " . ($show_detail_option == 'yes' ? 'Yes' : 'No') . "</p>"; // Hidden as per plan.md
    //echo "<p>Show Total Aging: " . ($show_total_aging_option == 'yes' ? 'Yes' : 'No') . "</p>"; // Hidden as per plan.md
} // This closes the if ($display_second_row_filters) block
echo '</div>';

// --- Display the Aging Report Table ---
if ($display_second_row_filters && $action_trigger) { // Use $action_trigger here as well
    if (!empty($report_data_display)) {
        echo '<table class="report-table" width="100%">'; // Applied report-table class
        echo '<tr class="liste_titre">';

        // Customer Name Header - Sortable
        // Construct URL parameters for sort links, preserving current filters
        $sort_url_params = '&filter_warehouse='.urlencode($selected_warehouse)
                         . '&filter_customer='.urlencode($selected_customer)
                         . '&filter_month='.$selected_month
                         . '&filter_year='.$selected_year
                         . '&show_detail_option='.$show_detail_option
                         . '&show_total_aging_option='.$show_total_aging_option
                         . '&filter_currency_usd='.$selected_currency_usd // Add currency checkbox state to sort params
                         . '&filter=FILTER'; // Crucial to trigger data display logic on GET

        echo '<th class="text-left sortable" onclick="window.location=\''.$_SERVER['PHP_SELF']
             .'?sort_field=customer_name'
             .'&sort_order='.($sort_field == 'customer_name' && $sort_order == 'asc' ? 'desc' : 'asc')
             .$sort_url_params.'\'">';
        echo 'Customer Name (sort)';
        if ($sort_field == 'customer_name') {
            echo ' <span class="sort-icon sort-'.$sort_order.'"></span>';
        }
        echo '</th>';

        // Headers based on plan: Customer Name, 120 Days+, 90 Days, 60 Days, 30 Days, Current, Total Due
        echo '<th>120 Days+</th>';
        echo '<th>90 Days (' . dol_escape_htmltag(date('M', mktime(0,0,0, $selected_month-3, 1, $selected_year))) . ')</th>';
        echo '<th>60 Days (' . dol_escape_htmltag(date('M', mktime(0,0,0, $selected_month-2, 1, $selected_year))) . ')</th>';
        echo '<th>30 Days (' . dol_escape_htmltag(date('M', mktime(0,0,0, $selected_month-1, 1, $selected_year))) . ')</th>';
        echo '<th>Current (' . dol_escape_htmltag(date('M Y', mktime(0,0,0, $selected_month, 1, $selected_year))) . ')</th>';
        // Total Due Header - Sortable
        echo '<th class="sortable" onclick="window.location=\''.$_SERVER['PHP_SELF']
             .'?sort_field=total_due'
             .'&sort_order='.($sort_field == 'total_due' && $sort_order == 'asc' ? 'desc' : 'asc')
             .$sort_url_params.'\'">';
        echo 'Total Due (sort)';
        if ($sort_field == 'total_due') {
            echo ' <span class="sort-icon sort-'.$sort_order.'"></span>';
        }
        echo '</th>';
        echo '</tr>';

        if ($show_total_aging_option == 'yes') {
            echo '<tr class="group-header">'; // Changed class from liste_total and removed inline style
            echo '<td class="text-left">Total for selected Customers:</td>'; // Added text-left class
             echo '<td class="amount">' . price($total_aging_summary['120 Days+'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
           echo '<td class="amount">' . price($total_aging_summary['90 Days'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
           echo '<td class="amount">' . price($total_aging_summary['60 Days'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
           echo '<td class="amount">' . price($total_aging_summary['30 Days'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
           echo '<td class="amount">' . price($total_aging_summary['Current'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
           echo '<td class="amount"><strong>' . price($total_aging_summary['total_excl_future'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</strong></td>';
            echo '</tr>';
        }

        $class_pair = true;
        foreach ($report_data_display as $customer_id => $data) {
            $class_pair = !$class_pair;
            echo '<tr class="'.($class_pair ? 'pair' : 'impair').'">';
            echo '<td class="text-left">' . $data['customer_name'] . '</td>'; // Added text-left class
            echo '<td>' . price($data['120 Days+'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>'; // text-align:right is default for td in new CSS
            echo '<td>' . price($data['90 Days'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
            echo '<td>' . price($data['60 Days'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
            echo '<td>' . price($data['30 Days'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
            echo '<td>' . price($data['Current'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
            echo '<td><strong>' . price($data['total_excl_future'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</strong></td>';
            echo '</tr>';

            if ($show_detail_option == 'yes' && !empty($data['invoices'])) {
                echo '<tr class="'.($class_pair ? 'pair' : 'impair').' detail-row"><td colspan="7" style="padding-left: 20px;">'; // Added detail-row class
                echo '<table class="nested-table" width="100%">'; // Applied nested-table class
                // Updated headers for the detail table as per plan.md and 1customer_aging.php
                echo '<tr class="liste_titre_sub">';
                echo '<th class="text-left">Invoice Ref</th>';
                echo '<th class="text-left">Validation Date</th>';
                echo '<th>120 Days+</th>';
                echo '<th>90 Days</th>';
                echo '<th>60 Days</th>';
                echo '<th>30 Days</th>';
                echo '<th>Current</th>';
                echo '<th>Total Due</th>';
                echo '</tr>';
            foreach ($data['invoices'] as $invoice) {
                    echo '<tr>'; // Start of an individual invoice row in the nested table
                    // Make Invoice Ref clickable to open invoice card in new tab
                    echo '<td class="text-left">';
                    echo '<a href="'.DOL_URL_ROOT.'/compta/facture/card.php?facid='.$invoice['invoice_id'].'" target="_blank">';
                    echo $invoice['invoice_ref'];
                    echo '</a></td>';
                    echo '<td class="text-left">' . $invoice['validation_date'] . '</td>';
                    echo '<td>' . price(($invoice['aging_period'] == '120 Days+') ? $invoice['balance_due'] : 0, 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
                    echo '<td>' . price(($invoice['aging_period'] == '90 Days') ? $invoice['balance_due'] : 0, 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
                    echo '<td>' . price(($invoice['aging_period'] == '60 Days') ? $invoice['balance_due'] : 0, 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
                    echo '<td>' . price(($invoice['aging_period'] == '30 Days') ? $invoice['balance_due'] : 0, 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
                    echo '<td>' . price(($invoice['aging_period'] == 'Current') ? $invoice['balance_due'] : 0, 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</td>';
                    echo '<td><strong>' . price($invoice['balance_due'], 0, $selected_currency_usd == 'yes' ? 'USD' : 'CAD') . '</strong></td>';
                    echo '</tr>'; // End of an individual invoice row
                } // End of foreach loop for invoices

                echo '</table>';
                echo '</td></tr>';
                // Add a blank row for visual separation, as per plan.md and 1customer_aging.php
                echo '<tr class="spacing-row"><td colspan="7">&nbsp;</td></tr>';
            }
         }
         echo '</table>';
     } else {
        echo '<div class="info">No aging data to display for the selected criteria.</div>';
    }
}

// echo '</div>'; // Removed closing tag for fichehalfleft
// echo '</div>'; // Removed closing tag for fichecenter

echo '<style type="text/css">
/* General table styling for this report page */
.report-table { border-collapse: collapse; width: 100%; margin-top: 20px; }
.report-table th, .report-table td { border: 1px solid #ddd; padding: 8px; }
.report-table th { background-color: #f2f2f2; text-align: right; white-space: normal; width: auto; } 
/* Default for headers, allow wrapping and auto width */
.report-table td { text-align: right; } /* Default for data cells */

/* Specific alignment for text columns */
.report-table th.text-left, .report-table td.text-left { text-align: left; }

/* Nested table for details */
.nested-table { width: 100%; margin-top: 5px; margin-bottom: 5px; border: 1px solid #e0e0e0; border-collapse: collapse; }
.nested-table th, .nested-table td { padding: 5px; border: 1px solid #e7e7e7; }
.nested-table th { background-color: #f0f0f0; text-align: right; } /* Default for nested headers */
.nested-table td { text-align: right; } /* Default for nested data cells */
.nested-table th.text-left, .nested-table td.text-left { text-align: left; width: 12%; } /* Invoice Ref & Validation Date columns */
.nested-table th:not(.text-left), .nested-table td:not(.text-left) { width: 12%; } /* The 6 amount columns (80% / 6) */


/* Dolibarr specific classes will still apply for backgrounds etc. */
/* Style for the group header row, similar to 1customer_aging.php */
.group-header { background-color: #ddd; font-weight: bold; }
.group-header td { text-align: right; } /* Ensure amounts are right-aligned */
.group-header td.text-left { text-align: left; } /* Ensure label is left-aligned */
.report-table tr.group-header td.amount { color: green; } /* Make amounts in total row green */

/* Style for the Customer Name column (first column in header and data rows)
   to ensure sufficient width and alignment, similar to .customer-name in 1customer_aging.php */
.report-table > thead > tr > th.text-left:first-child,
.report-table > tbody > tr:not(.group-header):not(.detail-row) > td.text-left:first-child {
    min-width: 115px; /* Ensures the column has a decent minimum width */
    max-width: 116px;  /* Allows the column to expand if content is larger */
    width: 25%;       /* Set to 12% as per request, respecting min/max */
}
    /* Style for the other 6 columns (amount columns) in the main report table */
.report-table > thead > tr > th:not(:first-child),
.report-table > tbody > tr:not(.group-header):not(.detail-row):not(.spacing-row) > td:not(:first-child) {
    width: 12%;
}    

/* Styles for sortable table headers */
.sortable {
    cursor: pointer;
}
.report-table th.sortable:hover {
    background-color: #e9e9e9; /* Or a color consistent with Dolibarr\'s theme */
}
.sort-icon {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 5px;
    vertical-align: middle;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
}
.sort-asc { border-bottom: 4px solid #333; }
.sort-desc { border-top: 4px solid #333; }

/* Style for the spacing row, as in 1customer_aging.php */
.spacing-row {
    height: 10px;
}
</style>';

// End of page
llxFooter();
$db->close();
?>
