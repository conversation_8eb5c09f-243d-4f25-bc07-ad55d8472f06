# License Validation System for Aging Reports

## Overview
The aging reports in this module require a valid license for each month you want to view data. This document explains how the licensing system works and how to obtain licenses for new months.

## License Files
- License files are stored in the `License` directory
- Each license file is named with the pattern `YYYY-MM.lic` (e.g., `2025-01.lic` for January 2025)
- License files contain validation information including checksums to prevent tampering
- The system also recognizes the license_settings.json file which contains configuration for the licensing system

## How to Obtain a License
To obtain a license for a new month:
1. Contact your system administrator
2. The administrator will use the License_Administrator_2026 application to generate a license file
3. The license file will be placed in the `License` directory
4. Once the license file is in place, you can view reports for that month

## Troubleshooting
If you see a message indicating that a month is not licensed:
1. Verify that you have selected the correct month and year
2. Check if a license file exists in the `License` directory for the selected month
3. If the license file exists but is not being recognized, it may be corrupted or invalid
4. Contact your system administrator for assistance

## Security
- Do not attempt to modify license files manually
- Do not attempt to copy license files from one month to another
- The system includes multiple anti-copying measures:
  1. Each license file contains the month/year embedded in its encrypted content
  2. The validation system verifies that the embedded date matches the filename
  3. The license includes a cryptographic signature that incorporates the month/year
  4. The system performs application integrity checks to prevent tampering
- Copying a license file from one month to another will be detected and rejected
- Unauthorized modifications to the system or license files may result in the system becoming unusable
