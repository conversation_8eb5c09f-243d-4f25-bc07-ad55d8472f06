<?php
/**
 * Test script for license validation
 *
 * This script tests the license validation functions with the actual license files
 * to help diagnose issues with license validation.
 */

// Load the license validation functions
require_once __DIR__ . '/license_validation.php';

// Function to display test results
function display_test_result($test_name, $result, $message = '') {
    echo "<div style='margin: 10px 0; padding: 10px; border-radius: 4px; " .
         ($result ? "background-color: #dff0d8; color: #3c763d;" : "background-color: #f2dede; color: #a94442;") . "'>" .
         "<strong>" . ($result ? "PASS" : "FAIL") . ":</strong> $test_name" .
         ($message ? "<br>$message" : "") .
         "</div>";
}

// Get the current month and year
$current_month = date('n');
$current_year = date('Y');

// HTML header
echo "<!DOCTYPE html>
<html>
<head>
    <title>License Validation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        .test-section { margin-bottom: 30px; }
        .file-list { margin: 10px 0; padding: 10px; background-color: #f8f8f8; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>License Validation Test</h1>";

// Test 1: List all license files
echo "<div class='test-section'>
    <h2>License Files in Directory</h2>";

$license_dir = __DIR__ . '/';
$license_files = glob($license_dir . '*.lic');

if (empty($license_files)) {
    echo "<div class='file-list'>No license files found in $license_dir</div>";
} else {
    echo "<div class='file-list'>";
    foreach ($license_files as $file) {
        $filename = basename($file);
        $filesize = filesize($file);
        $modified = date('Y-m-d H:i:s', filemtime($file));
        echo "File: $filename | Size: $filesize bytes | Modified: $modified<br>";

        // Display file content (first 100 characters)
        $content = file_get_contents($file);
        if ($content !== false) {
            $preview = substr($content, 0, 100) . (strlen($content) > 100 ? '...' : '');
            echo "Content preview: <pre>" . htmlspecialchars($preview) . "</pre><br>";
        }
    }
    echo "</div>";
}

// Test 2: Check current month license
echo "<div class='test-section'>
    <h2>Current Month License Test</h2>";

$is_current_month_licensed = is_month_licensed($current_month, $current_year);
display_test_result(
    "License validation for current month ($current_month/$current_year)",
    $is_current_month_licensed,
    $is_current_month_licensed ? "Current month is licensed" : "Current month is NOT licensed"
);

// Test 3: Check specific months
echo "<div class='test-section'>
    <h2>Specific Month Tests</h2>";

// Test months from license files
foreach ($license_files as $file) {
    $filename = basename($file);
    if (preg_match('/^(\d{4})-(\d{2})\.lic$/', $filename, $matches)) {
        $year = (int)$matches[1];
        $month = (int)$matches[2];

        $is_licensed = is_month_licensed($month, $year);
        display_test_result(
            "License validation for $filename",
            $is_licensed,
            $is_licensed ? "Month $month/$year is licensed" : "Month $month/$year is NOT licensed"
        );
    }
}

// Test 4: Check license file properties
echo "<div class='test-section'>
    <h2>License File Properties</h2>";

foreach ($license_files as $file) {
    $filename = basename($file);
    if (preg_match('/^(\d{4})-(\d{2})\.lic$/', $filename, $matches)) {
        $year = $matches[1];
        $month = $matches[2];
        $date_str = "$year-$month";

        // Check file size
        $filesize = filesize($file);
        $size_ok = $filesize > 0;

        // Check file modification time
        $file_modified = filemtime($file);
        $one_year_ago = time() - (365 * 24 * 60 * 60);
        $age_ok = $file_modified >= $one_year_ago;

        // Display file properties
        echo "<div class='file-properties'>
            <strong>File: $filename</strong><br>
            Size: $filesize bytes " . ($size_ok ? "<span style='color:green'>(OK)</span>" : "<span style='color:red'>(Empty!)</span>") . "<br>
            Modified: " . date('Y-m-d H:i:s', $file_modified) . " " . ($age_ok ? "<span style='color:green'>(OK)</span>" : "<span style='color:red'>(Too old!)</span>") . "
            </div>";

        // Test validation
        $is_valid = is_month_licensed((int)$month, (int)$year);
        display_test_result(
            "Validation check for $filename",
            $is_valid,
            $is_valid
                ? "File passes validation for $date_str"
                : "File fails validation for $date_str"
        );
    }
}

// HTML footer
echo "</body></html>";
?>
