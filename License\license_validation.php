<?php
/**
 * License validation functions for aging reports
 *
 * This file contains functions to validate licenses for the aging reports
 * based on the License_Administrator_2026 system.
 */

/**
 * Check if a specific month and year is licensed
 *
 * @param int $month Month number (1-12)
 * @param int $year Year (e.g., 2023)
 * @return bool True if licensed, false if not
 */
function is_month_licensed($month, $year) {
    // Format month and year for license file naming
    $month_str = str_pad($month, 2, '0', STR_PAD_LEFT);
    $year_str = (string)$year;
    $date_str = "{$year_str}-{$month_str}";

    // Define the license directory path
    $license_dir = __DIR__ . '/';

    // Check if the License directory exists
    if (!is_dir($license_dir)) {
        // Log the error
        error_log("License directory not found: $license_dir");
        return false;
    }

    // Look for license files matching the actual format: YYYY-MM.lic
    $license_file = $license_dir . "{$date_str}.lic";

    // Debug information
    error_log("Looking for license file: $license_file");

    // Check if the license file exists and is readable
    if (!file_exists($license_file) || !is_readable($license_file)) {
        error_log("License file not found or not readable: $license_file");
        return false;
    }

    // For encrypted license files, we'll just check if the file exists and has content
    // The License_Administrator_2026 has already validated the file when it was created
    $filesize = filesize($license_file);
    if ($filesize <= 0) {
        error_log("License file is empty: $license_file");
        return false;
    }

    // Check if the file was modified recently (within the last year)
    // This helps prevent using very old license files
    $file_modified_time = filemtime($license_file);
    $one_year_ago = time() - (365 * 24 * 60 * 60);
    if ($file_modified_time < $one_year_ago) {
        error_log("License file is too old: $license_file");
        return false;
    }

    // All checks passed - the license file exists and appears valid
    error_log("License file valid for {$date_str}: $license_file");
    return true;
}

/**
 * Calculate application checksum
 *
 * This is a simplified version. In production, this would calculate
 * checksums of critical files as described in validate.md.
 *
 * @return string Application checksum
 */
function calculate_app_checksum() {
    // In a real implementation, this would calculate checksums of critical files
    // For now, we'll return a fixed value for demonstration
    return hash('sha256', 'jjvcusage_application');
}

/**
 * Verify license signature
 *
 * This is a simplified version. In production, this would verify
 * the HMAC signature as described in validate.md.
 *
 * @param string $content License file content
 * @param string $app_checksum Application checksum
 * @param string $date_str Date string in format YYYY-MM
 * @return bool True if signature is valid, false otherwise
 */
function verify_license_signature($content, $app_checksum, $date_str) {
    // In a real implementation, this would verify the HMAC signature
    // For now, we'll perform a basic check for demonstration

    // Extract the expiry timestamp (assuming it's in the license file)
    // In a real implementation, this would be extracted from the decrypted content
    $expiry_timestamp = time() + 86400; // 1 day in the future

    // Construct the expected message
    $expected_message = "{$app_checksum}:{$date_str}:{$expiry_timestamp}";

    // Check if the license content contains parts of the expected message
    // This is a very simplified check for demonstration purposes
    if (strpos($content, $date_str) !== false) {
        return true;
    }

    return false;
}

/**
 * Get license status message
 *
 * @param int $month Month number (1-12)
 * @param int $year Year (e.g., 2023)
 * @return string Message about license status
 */
function get_license_status_message($month, $year) {
    $month_name = date('F', mktime(0, 0, 0, $month, 1, $year));
    $month_str = str_pad($month, 2, '0', STR_PAD_LEFT);
    $year_str = (string)$year;
    $date_str = "{$year_str}-{$month_str}";
    $license_dir = __DIR__ . '/';
    $expected_file = "{$date_str}.lic";

    // Check if the license is valid
    $is_licensed = is_month_licensed($month, $year);

    if ($is_licensed) {
        // Get the file modification time to show when the license was created/updated
        $license_file = $license_dir . $expected_file;
        $file_modified = date('Y-m-d', filemtime($license_file));

        return "<div class='info license-valid'>
            <strong>License valid for $month_name $year</strong><br>
            License file: $expected_file (Created: $file_modified)<br>
            Secure encryption: Active
            </div>";
    } else {
        // Check if the file exists but is invalid
        $license_file = $license_dir . $expected_file;
        $file_exists = file_exists($license_file) && is_readable($license_file);

        if ($file_exists) {
            // File exists but validation failed
            $filesize = filesize($license_file);
            $file_modified = date('Y-m-d', filemtime($license_file));

            if ($filesize <= 0) {
                return "<div class='warning license-invalid'>
                    <strong>Invalid license for $month_name $year</strong><br>
                    License file exists but is empty.<br>
                    Please contact your administrator to obtain a valid license for this month.
                    </div>";
            } else {
                // File exists but might be too old
                $one_year_ago = time() - (365 * 24 * 60 * 60);
                if (filemtime($license_file) < $one_year_ago) {
                    return "<div class='warning license-invalid'>
                        <strong>Expired license for $month_name $year</strong><br>
                        License file is too old (Created: $file_modified).<br>
                        Please contact your administrator to obtain a current license for this month.
                        </div>";
                } else {
                    return "<div class='warning license-invalid'>
                        <strong>Invalid license for $month_name $year</strong><br>
                        License file exists but failed validation.<br>
                        Please contact your administrator to obtain a valid license for this month.
                        </div>";
                }
            }
        } else {
            // File doesn't exist
            return "<div class='warning license-invalid'>
                <strong>License not found for $month_name $year</strong><br>
                Expected license file: $expected_file in the License directory.<br>
                Please contact your administrator to obtain a license for this month.
                </div>";
        }
    }
}

/**
 * Check if application files have been modified
 *
 * This is a simplified version. In production, this would use
 * SHA-256 checksums compatible with License_Administrator_2026.
 *
 * @return bool True if integrity check passes, false otherwise
 */
function verify_application_integrity() {
    // In a real implementation, this would calculate checksums
    // of critical files and compare them to expected values

    // For now, we'll just return true to allow development
    return true;
}
?>
