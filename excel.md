## Export to Excel Functionality Summary

This module provides functionality to export lists from Dolibarr to Excel files. The export process involves several key components:

**1. Module Configuration (`core/modules/modDoliExportList.class.php`):**

*   This file defines the module's settings, permissions, and dependencies.
*   It initializes the module and defines the "Export list" right.
*   It sets a constant `LIST_EXPORT_IMPORT_USE_COMPACT_MODE` to control the display mode of export buttons.

**2. Format Definition (`class/doliexportlist.class.php`):**

*   This file defines the `DoliExportList` class, which manages export formats.
*   The `getFormats()` method retrieves available export formats from the database (`llx_doliexportlist_format` table).
*   The `isActiveFormat()` method checks if a specific format is active.
*   The `enable()` and `disable()` methods allow enabling or disabling export formats.
*   The `setPosition()`, `up()`, and `down()` methods manage the order of export formats.

**3. Export Logic (`lib/doliexportlist.lib.php`):**

*   This file contains helper functions for the module.
*   `doliexportlistAdminPrepareHead()` prepares the head for admin pages.
*   `getButton()` generates HTML for export buttons.
*   `getCompactedButtons()` creates a compact dropdown menu for export options.
*   `exportTable()` exports data from a database table to a CSV or SQL format.
*   `exportCSV()` exports data from a CSV string to an SQL format.
*   `getModuleinfoFromUrl()` extracts module information from a URL.
*   `getTablename()` retrieves the table name associated with a module.
*   `getMoreTablenames()` retrieves additional table names related to a module.

**4. Excel Generation (`js/xlsx.full.min.js`):**

*   This file includes the `xlsx.js` library, which is responsible for generating Excel files in the XLSX format.
*   It provides functions for creating worksheets, adding data, and formatting cells.

**Overall Process:**

1.  The user triggers an export action from a Dolibarr list.
2.  The module retrieves the available and active export formats.
3.  The module uses the `xlsx.js` library to create an XLSX file with the data from the list.
4.  The user downloads the generated XLSX file.
