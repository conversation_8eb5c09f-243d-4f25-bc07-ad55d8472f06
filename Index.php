<?php
// Load Dolibarr environment
$res = 0;
if (!$res && file_exists("../../main.inc.php")) $res = @include '../../main.inc.php'; // For custom modules
if (!$res && file_exists("../../../main.inc.php")) $res = @include '../../../main.inc.php'; // For deeper paths
if (!$res) die("Include of main fails");

// Check user rights
// Allow access to the index page for all authenticated users
// Specific permissions will be checked in the individual report pages

// Load classes
require_once DOL_DOCUMENT_ROOT . '/core/lib/admin.lib.php';

// Output the page
llxHeader('', 'Aging Reports');
print_fiche_titre('Aging Reports', '', 'bill');

print '<div class="fichecenter">';
print '<div class="fichethirdleft">';

// First box
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th>Available Reports</th>';
print '</tr>';

// Customer Aging Report
print '<tr class="oddeven">';
print '<td><a href="'.DOL_URL_ROOT.'/custom/gtbcusage/Report/customer_aging.php">Customer Age</a></td>';
print '</tr>';

// Customer Aging Branch Total Report
// print '<tr class="oddeven">';
// print '<td><a href="'.DOL_URL_ROOT.'/custom/gtbcusage/Report/customer_aging_branch_total.php">Customer Age : Branch</a></td>';
// print '</tr>';

// Customer Aging Search Report
print '<tr class="oddeven">';
print '<td><a href="'.DOL_URL_ROOT.'/custom/gtbcusage/Report/customer_aging_search.php">Customer Age : Search</a></td>';
print '</tr>';

// Customer Statement email
// print '<tr class="oddeven">';
// print '<td><a href="'.DOL_URL_ROOT.'/custom/gtbcusage/Report/staat.php">Customer Statement Email</a></td>';
// print '</tr>';

// Supplier Aging Report
print '<tr class="oddeven">';
print '<td><a href="'.DOL_URL_ROOT.'/custom/gtbcusage/Report/supplier_aging.php">Supplier Age</a></td>';
print '</tr>';

// Supplier Aging Search Report
print '<tr class="oddeven">';
print '<td><a href="'.DOL_URL_ROOT.'/custom/gtbcusage/Report/supplier_aging_search.php">Supplier Age : Search</a></td>';
print '</tr>';

// Customer Balance Detail Report
// print '<tr class="oddeven">';
// print '<td><a href="'.DOL_URL_ROOT.'/custom/gtbcusage/Report/cusbaldet.php">Customer Balance : Search</a></td>';
// print '</tr>';

// print '</table>';
// print '</div>';

// print '</div>';
// print '<div class="fichetwothirdright">';

// Second box
print '<div class="div-table-responsive-no-min">';
print '<table class="noborder centpercent">';
print '<tr class="liste_titre">';
print '<th>About Aging Reports</th>';
print '</tr>';
print '<tr class="oddeven">';
print '<td>';
print '<p>The Aging Reports module provides detailed reports to track outstanding balances for both customers and suppliers.</p>';
print '<p><strong>Customer Aging Report:</strong> Track overdue customer invoices by aging periods (Current, 30 Days, 60 Days, 90 Days, 120+ Days).</p>';
print '<p><strong>Supplier Aging Report:</strong> Monitor your payables to suppliers by aging periods.</p>';
print '<p><strong>Customer Search Report:</strong> Search and filter customer aging data with detailed invoice information.</p>';
print '<p><strong>Supplier Search Report:</strong> Search and filter supplier aging data with detailed invoice information.</p>';
// print '<p><strong>Customer Statement Email:</strong> Email Customer Statements.</p>';
// print '<p><strong>Customer Balance Detail:</strong> Customer Balance Deatil Report.</p>';

print '</td>';
print '</tr>';
print '</table>';
print '</div>';

print '</div>';
print '</div>';

llxFooter();
$db->close();
?>
