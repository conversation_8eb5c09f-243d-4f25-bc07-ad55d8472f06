<?php
include_once DOL_DOCUMENT_ROOT.'/core/modules/DolibarrModules.class.php';

class modcustomerinterest extends DolibarrModules
{
    public function __construct($db)
    {
        // Call parent constructor FIRST.
        // This initializes properties like $this->db and $this->dirmodule.
        // $this->dirmodule is important if $this->page_setup is not used and
        // <PERSON><PERSON><PERSON><PERSON> falls back to building the URL from $this->config_page_url.
        parent::__construct($db);

        global $langs, $conf;

        // $this->db = $db; // Already set by parent constructor
        $this->numero = 504000;
        $this->rights_class = 'customerinterest';
        $this->family = "financial"; // Financial, Other, Transverse, Interface, Ecommerce, Project, HR, ...
        // The 'name' property must match the module's directory name exactly (case-sensitive).
        // Assuming your module directory is "CustomerInterest".
        $this->name = 'CustomerInterest';
        $this->description = "Customer Interest Reports";
        $this->descriptionlong = "This module provides customer interest reports.";
        $this->version = '1.0';
        $this->const_name = 'MAIN_MODULE_CUSTOMERINTEREST';
        $this->picto = 'customerinterest@CustomerInterest'; // Use module-specific icon: modulename@ModuleName

        // Explicitly define the setup page path including /custom/
        // This path is absolute from DOL_URL_ROOT if it starts with /.
        $this->page_setup = '/custom/CustomerInterest/admin/customerinterest_config.php';

        // This path should be relative to htdocs/custom/ModuleName/
        $this->config_page_url = array(
            'customerinterest_config.php@customerinterest');

        // Module parts
        $this->module_parts = array();
        $this->always_enabled = false; // Make sure this is not forced to be enabled
        $this->core_enabled = false;   // This is not a core module

        // Define permissions - remove the duplicate canuse permission
        $this->rights = array(
            0 => array(
                0 => '125035093',                    // Permission ID (unique number)
                1 => 'CustomerInterest_CanUse',     // Permission label
                2 => 'r',                            // Permission type (r=read)
                3 => 0,                              // Permission default (0=off by default)
                4 => 'canuse',                       // Permission key
                5 => '',                              // Permission description
            ),
            1 => array(
                0 => '125035098',
                1 => 'CustomerInterest_AddReport',
                2 => 'w',                            // w=write
                3 => 0,                              // Permission default (0=off by default)
                4 => 'addreport',
                5 => '',
            ),
            2 => array(
                0 => '125035099',
                1 => 'CustomerInterest_EditReport',
                2 => 'w',
                3 => 0,
                4 => 'editreport',
                5 => '',
            ),
            3 => array(
                0 => '125035090',
                1 => 'CustomerInterest_DeleteReport',
                2 => 'd',                            // d=delete
                3 => 0,
                4 => 'deletereport',
                5 => '',
            ),
            4 => array(
                0 => '125035091',
                1 => 'CustomerInterest_ExportReport',
                2 => 'r',                            // r=read
                3 => 0,                              // Permission default (0=off by default)
                4 => 'exportreport',
                5 => '',
            )
        );

        // Menu entries
        $this->menu = array();
        $r = 0;

        // Add main menu entry for Customer Interest Reports
        $this->menu[$r] = array(
            'fk_menu' => '',
            'type' => 'top',
            'titre' => 'Customer_Interest',
            'mainmenu' => 'customerinterest',
            'url' => '/custom/CustomerInterest/Index.php',
            'langs' => 'customerinterest@customerinterest',
            'position' => 100,
            'enabled' => '$conf->customerinterest->enabled',
            'perms' => '$user->rights->customerinterest->canuse',
            'target' => '',
            'user' => 2,
            'prefix' => '<span class="fas fa-chart-line pictofixedwidth valignmiddle"></span>'
        );
        $r++;

        // Add left menu entry for Customer Overdue Interest Report
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=customerinterest',
            'type' => 'left',
            'titre' => 'Overdue Interest',
            'mainmenu' => 'customerinterest',
            'leftmenu' => 'customerinterest_overdue',
            'url' => '/custom/CustomerInterest/Report/customer_overdue_interest.php',
            'langs' => 'customerinterest@customerinterest',
            'position' => 101,
            'enabled' => '$conf->customerinterest->enabled',
            'perms' => '$user->rights->customerinterest->canuse',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Add MANUAL left menu entry for Customer Overdue Interest Report
        // $this->menu[$r] = array(
        //     'fk_menu' => 'fk_mainmenu=customerinterest',
        //     'type' => 'left',
        //     'titre' => 'Overdue Interest Manual',
        //     'mainmenu' => 'customerinterest',
        //     'leftmenu' => 'customerinterest_overdue',
        //     'url' => '/custom/CustomerInterest/Report/MANcustomer_overdue_interest.php',
        //     'langs' => 'customerinterest@customerinterest',
        //     'position' => 102,
        //     'enabled' => '$conf->customerinterest->enabled',
        //     'perms' => '$user->rights->customerinterest->canuse',
        //     'target' => '',
        //     'user' => 2
        // );
        // $r++;

        // Add MANUAL left menu entry for Customer Overdue Interest Report
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=customerinterest',
            'type' => 'left',
            'titre' => 'Customers Excluded From Interest',
            'mainmenu' => 'customerinterest',
            'leftmenu' => 'customerinterest_overdue',
            'url' => '/custom/CustomerInterest/Report/Customers_Excluded_from_Interest.php',
            'langs' => 'customerinterest@customerinterest',
            'position' => 103,
            'enabled' => '$conf->customerinterest->enabled',
            'perms' => '$user->rights->customerinterest->canuse',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Add ABOUT left menu entry for Customer Overdue Interest Report
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=customerinterest',
            'type' => 'left',
            'titre' => 'About Overdue Interest Reports',
            'mainmenu' => 'customerinterest',
            'leftmenu' => 'customerinterest_overdue',
            'url' => '/custom/CustomerInterest/Report/about_customer_overdue_interest.php',
            'langs' => 'customerinterest@customerinterest',
            'position' => 104,
            'enabled' => '$conf->customerinterest->enabled',
            'perms' => '$user->rights->customerinterest->canuse',
            'target' => '',
            'user' => 2
        );
        $r++;

        
    }

    public function init($options = '')
    {
        $res = $this->_init(array(), $options);

        // Create constants if they do not exist
        $this->_load_other_const();

        return $res;
    }

    /**
     * Load other constants
     *
     * @return void
     */
    function _load_other_const()
    {
        global $conf, $db;

        // Load constants
        // Always retrieve entity from database
        $sql = "SELECT value FROM ".MAIN_DB_PREFIX."const WHERE nom = 'MAIN_INFO_SOCIETE_ID'";
        $result = $db->query($sql);
        if ($result && $obj = $db->fetch_object($result)) {
            $conf->entity = $obj->value;
        }

        if (!empty($conf->entity))
        {
            $sql = "SELECT nom, value FROM ".MAIN_DB_PREFIX."const WHERE entity = ".$conf->entity." AND nom LIKE 'CUSTOMERINTEREST_%'";
            $result = $this->db->query($sql);
            if ($result)
            {
                while ($obj = $this->db->fetch_object($result))
                {
                    $conf->global->{$obj->nom} = $obj->value;
                }
            }
        }
    }

    public function remove($options = '')
    {
        return $this->_remove(array(), $options);
    }
}
