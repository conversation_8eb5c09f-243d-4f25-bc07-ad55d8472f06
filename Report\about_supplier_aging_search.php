<?php
// Load Dolibarr environment
$res = 0;
if (!$res && file_exists("../../main.inc.php")) $res = @include '../../main.inc.php'; // For custom modules
if (!$res && file_exists("../../../main.inc.php")) $res = @include '../../../main.inc.php'; // For deeper paths
if (!$res) die("Include of main fails");

// Check user rights
if (empty($user->rights->societe->lire)) accessforbidden(); // Ensure the user has the required permissions

// Load license validation functions
require_once __DIR__ . '/../License/license_validation.php';

// Page title
$title = "About Supplier Aging Search Report";
llxHeader('', $title);

print_fiche_titre($title, '', 'bill');

// Add close button at the top
echo '<div class="close-button-container top-button">
    <button class="close-button" onclick="window.close();">Close</button>
</div>';
?>

<div class="about-container">
    <h2>Overview</h2>
    <p>The Supplier Aging Search Report provides a detailed view of outstanding supplier invoices with advanced search capabilities. This report helps you identify and manage overdue payments to suppliers by categorizing them into aging periods.</p>

    <h2>Features</h2>
    <ul>
        <li><strong>Branch Filter:</strong> Filter suppliers by branch location</li>
        <li><strong>Payment Term:</strong> Filter by All or specific payment terms for suppliers</li>
        <li><strong>Month/Year Selection:</strong> View aging data as of the end of any month</li>
        <li><strong>Filter Button:</strong> Apply filters and reset search fields</li>
        <li><strong>Show Payment Term:</strong> When selected, displays payment terms in the report</li>
        <li><strong>Search Functionality:</strong> Search suppliers by name or code</li>
        <li><strong>Aging Categories:</strong> Invoices are categorized into aging periods:
            <ul>
                <li>Future: Invoices not yet due</li>
                <li>Current: 0-30 days</li>
                <li>30 Days: 31-60 days</li>
                <li>60 Days: 61-90 days</li>
                <li>90 Days: 91-120 days</li>
                <li>120 Days+: Over 120 days</li>
            </ul>
        </li>
        <li><strong>Detailed View:</strong> Expand each supplier to see individual invoice details</li>
    </ul>

    <h2>How to Use</h2>
    <ol>
        <li>Select a branch from the "Branch" dropdown to filter suppliers by location</li>
        <li>Choose a payment term or "All Payment Terms" from the dropdown</li>
        <li>Select the month and year for the aging report</li>
        <li>Check "Show Payment Term" if you want to display payment terms in the report</li>
        <li>Click "Filter" to apply your selections and reset search fields</li>
        <li>Use the search boxes to filter suppliers by code or name</li>
        <li>Click on a supplier row to view detailed invoice information</li>
    </ol>

    <h2>Search Functions</h2>
    <ul>
        <li><strong>Search Code:</strong> Type in this field to quickly filter suppliers by their supplier code</li>
        <li><strong>Search Supplier Name:</strong> Type in this field to filter suppliers by their name</li>
    </ul>
    <p>Both search fields work in real-time as you type. You can use them individually, but not together - typing in one field will clear the other.</p>

    <h2>Filter Button</h2>
    <p>The Filter button applies your selected criteria (branch, payment term, month, year) and has these additional effects:</p>
    <ul>
        <li>Collapses all expanded supplier details</li>
        <li>Resets the view to show only the main supplier rows</li>
        <li>Clears any active search filters</li>
    </ul>
    <p>Use this button when you want to start fresh with new filter criteria.</p>

    <h2>Understanding the Report</h2>
    <p>The report displays outstanding invoices categorized by aging periods. Each row represents a supplier with their total outstanding balance broken down by aging category.</p>
    <p>The "Total Excl Future" column shows the sum of all currently due amounts (excluding future payments), while the "Total" column includes all outstanding amounts including future payments.</p>
    <p>Expanding a supplier row shows individual invoices with their reference numbers, dates, and aging information.</p>

    <h2>Licensing</h2>
    <p>This report requires a valid license for the selected month. If the selected month is not licensed, the report data will not be available.</p>

    <h2>Tips for Effective Use</h2>
    <ul>
        <li>Regularly review the aging categories to identify overdue accounts</li>
        <li>Use the search function to quickly locate specific suppliers</li>
        <li>Monitor trends in aging categories to identify payment pattern changes</li>
        <li>Pay attention to the "Total Excl Future" column to focus on current obligations</li>
    </ul>

    <h2>Support</h2>
    <p>For questions or support regarding this report, please contact your system administrator or support team.</p>
</div>

<div class="close-button-container bottom-button">
    <button class="close-button" onclick="window.close();">Close</button>
</div>

<style>
.about-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    font-family: Arial, sans-serif;
    line-height: 1.5;
}

h2 {
    color: #2b4570;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
    margin-top: 20px;
}

ul, ol {
    margin-left: 20px;
}

li {
    margin-bottom: 5px;
}

.close-button-container {
    text-align: center;
    margin: 15px 0;
}

.top-button {
    margin-bottom: 20px;
}

.bottom-button {
    margin-top: 30px;
}

.close-button {
    padding: 8px 20px;
    background-color: #2b4570;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
}

.close-button:hover {
    background-color: #1a2a43;
}
</style>

<?php
llxFooter();
$db->close();
?>
